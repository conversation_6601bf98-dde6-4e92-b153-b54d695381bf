/**
 * Tests for StatusIndicator Component
 * ==================================
 * 
 * This module contains tests for the StatusIndicator component.
 * All tests use mocked stores and services to avoid dependencies on backend.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import StatusIndicator from '../src/components/StatusIndicator.vue'
import { useStatusStore } from '../src/stores/status'

// Mock the status store
vi.mock('../src/stores/status', () => ({
  useStatusStore: vi.fn()
}))

describe('StatusIndicator', () => {
  let wrapper
  let mockStatusStore

  beforeEach(() => {
    // Create a fresh Pinia instance for each test
    setActivePinia(createPinia())
    
    // Create mock status store
    mockStatusStore = {
      isConnected: true,
      isHealthy: true,
      lastError: null,
      connectWebSocket: vi.fn(),
      disconnectWebSocket: vi.fn()
    }
    
    // Mock the useStatusStore function
    useStatusStore.mockReturnValue(mockStatusStore)
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
  })

  it('renders correctly when connected and healthy', () => {
    // Arrange
    mockStatusStore.isConnected = true
    mockStatusStore.isHealthy = true
    
    // Act
    wrapper = mount(StatusIndicator)
    
    // Assert
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.find('.status-indicator').exists()).toBe(true)
    expect(wrapper.find('.status-dot').exists()).toBe(true)
    expect(wrapper.find('.status-text').exists()).toBe(true)
    expect(wrapper.find('.status-text').text()).toBe('Connected')
    expect(wrapper.find('.status-dot').classes()).toContain('status-dot--connected')
  })

  it('renders correctly when disconnected', () => {
    // Arrange
    mockStatusStore.isConnected = false
    mockStatusStore.isHealthy = false
    
    // Act
    wrapper = mount(StatusIndicator)
    
    // Assert
    expect(wrapper.find('.status-text').text()).toBe('Disconnected')
    expect(wrapper.find('.status-dot').classes()).toContain('status-dot--disconnected')
  })

  it('renders correctly when connected but unhealthy', () => {
    // Arrange
    mockStatusStore.isConnected = true
    mockStatusStore.isHealthy = false
    mockStatusStore.lastError = 'Recoater error'
    
    // Act
    wrapper = mount(StatusIndicator)
    
    // Assert
    expect(wrapper.find('.status-text').text()).toBe('Error')
    expect(wrapper.find('.status-dot').classes()).toContain('status-dot--error')
  })

  it('calls connectWebSocket on mount', () => {
    // Act
    wrapper = mount(StatusIndicator)
    
    // Assert
    expect(mockStatusStore.connectWebSocket).toHaveBeenCalledOnce()
  })

  it('calls disconnectWebSocket on unmount', () => {
    // Arrange
    wrapper = mount(StatusIndicator)
    
    // Act
    wrapper.unmount()
    
    // Assert
    expect(mockStatusStore.disconnectWebSocket).toHaveBeenCalledOnce()
  })

  it('displays correct tooltip for connected state', () => {
    // Arrange
    mockStatusStore.isConnected = true
    mockStatusStore.isHealthy = true
    
    // Act
    wrapper = mount(StatusIndicator)
    
    // Assert
    expect(wrapper.find('.status-dot').attributes('title')).toBe('System operational')
  })

  it('displays correct tooltip for disconnected state', () => {
    // Arrange
    mockStatusStore.isConnected = false
    
    // Act
    wrapper = mount(StatusIndicator)
    
    // Assert
    expect(wrapper.find('.status-dot').attributes('title')).toBe('Backend connection lost')
  })

  it('displays correct tooltip for error state', () => {
    // Arrange
    mockStatusStore.isConnected = true
    mockStatusStore.isHealthy = false
    mockStatusStore.lastError = 'Connection timeout'
    
    // Act
    wrapper = mount(StatusIndicator)
    
    // Assert
    expect(wrapper.find('.status-dot').attributes('title')).toBe('Recoater error: Connection timeout')
  })

  it('displays correct tooltip for error state without specific error', () => {
    // Arrange
    mockStatusStore.isConnected = true
    mockStatusStore.isHealthy = false
    mockStatusStore.lastError = null
    
    // Act
    wrapper = mount(StatusIndicator)
    
    // Assert
    expect(wrapper.find('.status-dot').attributes('title')).toBe('Recoater error: Unknown error')
  })
})
