{"version": 3, "sources": ["../../../src/js/parser.js"], "sourcesContent": ["/**\n * parser.js\n */\n\n/* import */\nimport { findAll, parse, toPlainObject, walk } from 'css-tree';\n\n/* constants */\nimport {\n  DUO, HEX, MAX_BIT_16, BIT_HYPHEN, REG_LOGICAL_PSEUDO, REG_SHADOW_PSEUDO,\n  SELECTOR, SELECTOR_PSEUDO_CLASS, SELECTOR_PSEUDO_ELEMENT, SYNTAX_ERR,\n  TYPE_FROM, TYPE_TO, U_FFFD\n} from './constant.js';\n\n/**\n * unescape selector\n * @param {string} selector - CSS selector\n * @returns {?string} - unescaped selector\n */\nexport const unescapeSelector = (selector = '') => {\n  if (typeof selector === 'string' && selector.indexOf('\\\\', 0) >= 0) {\n    const arr = selector.split('\\\\');\n    const l = arr.length;\n    for (let i = 1; i < l; i++) {\n      let item = arr[i];\n      if (item === '' && i === l - 1) {\n        item = U_FFFD;\n      } else {\n        const hexExists = /^([\\da-f]{1,6}\\s?)/i.exec(item);\n        if (hexExists) {\n          const [, hex] = hexExists;\n          let str;\n          try {\n            const low = parseInt('D800', HEX);\n            const high = parseInt('DFFF', HEX);\n            const deci = parseInt(hex, HEX);\n            if (deci === 0 || (deci >= low && deci <= high)) {\n              str = U_FFFD;\n            } else {\n              str = String.fromCodePoint(deci);\n            }\n          } catch (e) {\n            str = U_FFFD;\n          }\n          let postStr = '';\n          if (item.length > hex.length) {\n            postStr = item.substring(hex.length);\n          }\n          item = `${str}${postStr}`;\n        // whitespace\n        } else if (/^[\\n\\r\\f]/.test(item)) {\n          item = '\\\\' + item;\n        }\n      }\n      arr[i] = item;\n    }\n    selector = arr.join('');\n  }\n  return selector;\n};\n\n/**\n * preprocess\n * @see https://drafts.csswg.org/css-syntax-3/#input-preprocessing\n * @param {...*} args - arguments\n * @returns {string} - filtered selector string\n */\nexport const preprocess = (...args) => {\n  if (!args.length) {\n    throw new TypeError('1 argument required, but only 0 present.');\n  }\n  let [selector] = args;\n  if (typeof selector === 'string') {\n    let index = 0;\n    while (index >= 0) {\n      index = selector.indexOf('#', index);\n      if (index < 0) {\n        break;\n      }\n      const preHash = selector.substring(0, index + 1);\n      let postHash = selector.substring(index + 1);\n      const codePoint = postHash.codePointAt(0);\n      // @see https://drafts.csswg.org/selectors/#id-selectors\n      // @see https://drafts.csswg.org/css-syntax-3/#ident-token-diagram\n      if (codePoint === BIT_HYPHEN) {\n        if (/^\\d$/.test(postHash.substring(1, 2))) {\n          throw new DOMException(`Invalid selector ${selector}`, SYNTAX_ERR);\n        }\n      // escape char above 0xFFFF\n      } else if (codePoint > MAX_BIT_16) {\n        const str = `\\\\${codePoint.toString(HEX)} `;\n        if (postHash.length === DUO) {\n          postHash = str;\n        } else {\n          postHash = `${str}${postHash.substring(DUO)}`;\n        }\n      }\n      selector = `${preHash}${postHash}`;\n      index++;\n    }\n    selector = selector.replace(/\\f|\\r\\n?/g, '\\n')\n      .replace(/[\\0\\uD800-\\uDFFF]|\\\\$/g, U_FFFD);\n  } else if (selector === undefined || selector === null) {\n    selector = Object.prototype.toString.call(selector)\n      .slice(TYPE_FROM, TYPE_TO).toLowerCase();\n  } else if (Array.isArray(selector)) {\n    selector = selector.join(',');\n  } else if (Object.prototype.hasOwnProperty.call(selector, 'toString')) {\n    selector = selector.toString();\n  } else {\n    throw new DOMException(`Invalid selector ${selector}`, SYNTAX_ERR);\n  }\n  return selector;\n};\n\n/**\n * create AST from CSS selector\n * @param {string} selector - CSS selector\n * @returns {object} - AST\n */\nexport const parseSelector = selector => {\n  selector = preprocess(selector);\n  // invalid selectors\n  if (/^$|^\\s*>|,\\s*$/.test(selector)) {\n    throw new DOMException(`Invalid selector ${selector}`, SYNTAX_ERR);\n  }\n  let res;\n  try {\n    const ast = parse(selector, {\n      context: 'selectorList',\n      parseCustomProperty: true\n    });\n    res = toPlainObject(ast);\n  } catch (e) {\n    // workaround for https://github.com/csstree/csstree/issues/265\n    // NOTE: still throws on `:lang(\"\")`;\n    const regLang = /(:lang\\(\\s*(\"[A-Za-z\\d\\-*]+\")\\s*\\))/;\n    if (e.message === 'Identifier is expected' && regLang.test(selector)) {\n      const [, lang, range] = regLang.exec(selector);\n      const escapedRange =\n        range.replaceAll('*', '\\\\*').replace(/^\"/, '').replace(/\"$/, '');\n      const escapedLang = lang.replace(range, escapedRange);\n      res = parseSelector(selector.replace(lang, escapedLang));\n    } else if (e.message === '\"]\" is expected' && !selector.endsWith(']')) {\n      res = parseSelector(`${selector}]`);\n    } else if (e.message === '\")\" is expected' && !selector.endsWith(')')) {\n      res = parseSelector(`${selector})`);\n    } else {\n      throw new DOMException(e.message, SYNTAX_ERR);\n    }\n  }\n  return res;\n};\n\n/**\n * walk AST\n * @param {object} ast - AST\n * @returns {Array.<object|undefined>} - collection of AST branches\n */\nexport const walkAST = (ast = {}) => {\n  const branches = new Set();\n  let hasPseudoFunc;\n  const opt = {\n    enter: node => {\n      if (node.type === SELECTOR) {\n        branches.add(node.children);\n      } else if ((node.type === SELECTOR_PSEUDO_CLASS &&\n                  REG_LOGICAL_PSEUDO.test(node.name)) ||\n                 (node.type === SELECTOR_PSEUDO_ELEMENT &&\n                  REG_SHADOW_PSEUDO.test(node.name))) {\n        hasPseudoFunc = true;\n      }\n    }\n  };\n  walk(ast, opt);\n  if (hasPseudoFunc) {\n    findAll(ast, (node, item, list) => {\n      if (list) {\n        if (node.type === SELECTOR_PSEUDO_CLASS &&\n            REG_LOGICAL_PSEUDO.test(node.name)) {\n          const itemList = list.filter(i => {\n            const { name, type } = i;\n            const res =\n              type === SELECTOR_PSEUDO_CLASS && REG_LOGICAL_PSEUDO.test(name);\n            return res;\n          });\n          for (const { children } of itemList) {\n            // SelectorList\n            for (const { children: grandChildren } of children) {\n              // Selector\n              for (const { children: greatGrandChildren } of grandChildren) {\n                if (branches.has(greatGrandChildren)) {\n                  branches.delete(greatGrandChildren);\n                }\n              }\n            }\n          }\n        } else if (node.type === SELECTOR_PSEUDO_ELEMENT &&\n                   REG_SHADOW_PSEUDO.test(node.name)) {\n          const itemList = list.filter(i => {\n            const { name, type } = i;\n            const res =\n              type === SELECTOR_PSEUDO_ELEMENT && REG_SHADOW_PSEUDO.test(name);\n            return res;\n          });\n          for (const { children } of itemList) {\n            // Selector\n            for (const { children: grandChildren } of children) {\n              if (branches.has(grandChildren)) {\n                branches.delete(grandChildren);\n              }\n            }\n          }\n        }\n      }\n    });\n  }\n  return [...branches];\n};\n\n/* export */\nexport { generate as generateCSS } from 'css-tree';\n"], "mappings": "4ZAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,8CAAAE,EAAA,eAAAC,EAAA,qBAAAC,EAAA,YAAAC,IAAA,eAAAC,EAAAN,GAKA,IAAAO,EAAoD,oBAGpDC,EAIO,yBAiNPD,EAAwC,oBA1MjC,MAAMH,EAAmB,CAACK,EAAW,KAAO,CACjD,GAAI,OAAOA,GAAa,UAAYA,EAAS,QAAQ,KAAM,CAAC,GAAK,EAAG,CAClE,MAAMC,EAAMD,EAAS,MAAM,IAAI,EACzBE,EAAID,EAAI,OACd,QAAS,EAAI,EAAG,EAAIC,EAAG,IAAK,CAC1B,IAAIC,EAAOF,EAAI,CAAC,EAChB,GAAIE,IAAS,IAAM,IAAMD,EAAI,EAC3BC,EAAO,aACF,CACL,MAAMC,EAAY,sBAAsB,KAAKD,CAAI,EACjD,GAAIC,EAAW,CACb,KAAM,CAAC,CAAEC,CAAG,EAAID,EAChB,IAAIE,EACJ,GAAI,CACF,MAAMC,EAAM,SAAS,OAAQ,KAAG,EAC1BC,EAAO,SAAS,OAAQ,KAAG,EAC3BC,EAAO,SAASJ,EAAK,KAAG,EAC1BI,IAAS,GAAMA,GAAQF,GAAOE,GAAQD,EACxCF,EAAM,SAENA,EAAM,OAAO,cAAcG,CAAI,CAEnC,MAAY,CACVH,EAAM,QACR,CACA,IAAII,EAAU,GACVP,EAAK,OAASE,EAAI,SACpBK,EAAUP,EAAK,UAAUE,EAAI,MAAM,GAErCF,EAAO,GAAGG,CAAG,GAAGI,CAAO,EAEzB,KAAW,YAAY,KAAKP,CAAI,IAC9BA,EAAO,KAAOA,EAElB,CACAF,EAAI,CAAC,EAAIE,CACX,CACAH,EAAWC,EAAI,KAAK,EAAE,CACxB,CACA,OAAOD,CACT,EAQaN,EAAa,IAAIiB,IAAS,CACrC,GAAI,CAACA,EAAK,OACR,MAAM,IAAI,UAAU,0CAA0C,EAEhE,GAAI,CAACX,CAAQ,EAAIW,EACjB,GAAI,OAAOX,GAAa,SAAU,CAChC,IAAIY,EAAQ,EACZ,KAAOA,GAAS,IACdA,EAAQZ,EAAS,QAAQ,IAAKY,CAAK,EAC/B,EAAAA,EAAQ,KAFK,CAKjB,MAAMC,EAAUb,EAAS,UAAU,EAAGY,EAAQ,CAAC,EAC/C,IAAIE,EAAWd,EAAS,UAAUY,EAAQ,CAAC,EAC3C,MAAMG,EAAYD,EAAS,YAAY,CAAC,EAGxC,GAAIC,IAAc,cAChB,GAAI,OAAO,KAAKD,EAAS,UAAU,EAAG,CAAC,CAAC,EACtC,MAAM,IAAI,aAAa,oBAAoBd,CAAQ,GAAI,YAAU,UAG1De,EAAY,aAAY,CACjC,MAAMT,EAAM,KAAKS,EAAU,SAAS,KAAG,CAAC,IACpCD,EAAS,SAAW,MACtBA,EAAWR,EAEXQ,EAAW,GAAGR,CAAG,GAAGQ,EAAS,UAAU,KAAG,CAAC,EAE/C,CACAd,EAAW,GAAGa,CAAO,GAAGC,CAAQ,GAChCF,GACF,CACAZ,EAAWA,EAAS,QAAQ,YAAa;AAAA,CAAI,EAC1C,QAAQ,yBAA0B,QAAM,CAC7C,SAAqCA,GAAa,KAChDA,EAAW,OAAO,UAAU,SAAS,KAAKA,CAAQ,EAC/C,MAAM,YAAW,SAAO,EAAE,YAAY,UAChC,MAAM,QAAQA,CAAQ,EAC/BA,EAAWA,EAAS,KAAK,GAAG,UACnB,OAAO,UAAU,eAAe,KAAKA,EAAU,UAAU,EAClEA,EAAWA,EAAS,SAAS,MAE7B,OAAM,IAAI,aAAa,oBAAoBA,CAAQ,GAAI,YAAU,EAEnE,OAAOA,CACT,EAOaP,EAAgBO,GAAY,CAGvC,GAFAA,EAAWN,EAAWM,CAAQ,EAE1B,iBAAiB,KAAKA,CAAQ,EAChC,MAAM,IAAI,aAAa,oBAAoBA,CAAQ,GAAI,YAAU,EAEnE,IAAIgB,EACJ,GAAI,CACF,MAAMC,KAAM,SAAMjB,EAAU,CAC1B,QAAS,eACT,oBAAqB,EACvB,CAAC,EACDgB,KAAM,iBAAcC,CAAG,CACzB,OAASC,EAAG,CAGV,MAAMC,EAAU,sCAChB,GAAID,EAAE,UAAY,0BAA4BC,EAAQ,KAAKnB,CAAQ,EAAG,CACpE,KAAM,CAAC,CAAEoB,EAAMC,CAAK,EAAIF,EAAQ,KAAKnB,CAAQ,EACvCsB,EACJD,EAAM,WAAW,IAAK,KAAK,EAAE,QAAQ,KAAM,EAAE,EAAE,QAAQ,KAAM,EAAE,EAC3DE,EAAcH,EAAK,QAAQC,EAAOC,CAAY,EACpDN,EAAMvB,EAAcO,EAAS,QAAQoB,EAAMG,CAAW,CAAC,CACzD,SAAWL,EAAE,UAAY,mBAAqB,CAAClB,EAAS,SAAS,GAAG,EAClEgB,EAAMvB,EAAc,GAAGO,CAAQ,GAAG,UACzBkB,EAAE,UAAY,mBAAqB,CAAClB,EAAS,SAAS,GAAG,EAClEgB,EAAMvB,EAAc,GAAGO,CAAQ,GAAG,MAElC,OAAM,IAAI,aAAakB,EAAE,QAAS,YAAU,CAEhD,CACA,OAAOF,CACT,EAOapB,EAAU,CAACqB,EAAM,CAAC,IAAM,CACnC,MAAMO,EAAW,IAAI,IACrB,IAAIC,EAaJ,iBAAKR,EAZO,CACV,MAAOS,GAAQ,CACTA,EAAK,OAAS,WAChBF,EAAS,IAAIE,EAAK,QAAQ,GAChBA,EAAK,OAAS,yBACd,qBAAmB,KAAKA,EAAK,IAAI,GACjCA,EAAK,OAAS,2BACd,oBAAkB,KAAKA,EAAK,IAAI,KAC1CD,EAAgB,GAEpB,CACF,CACa,EACTA,MACF,WAAQR,EAAK,CAACS,EAAMvB,EAAMwB,IAAS,CACjC,GAAIA,GACF,GAAID,EAAK,OAAS,yBACd,qBAAmB,KAAKA,EAAK,IAAI,EAAG,CACtC,MAAME,EAAWD,EAAK,OAAOE,GAAK,CAChC,KAAM,CAAE,KAAAC,EAAM,KAAAC,CAAK,EAAIF,EAGvB,OADEE,IAAS,yBAAyB,qBAAmB,KAAKD,CAAI,CAElE,CAAC,EACD,SAAW,CAAE,SAAAE,CAAS,IAAKJ,EAEzB,SAAW,CAAE,SAAUK,CAAc,IAAKD,EAExC,SAAW,CAAE,SAAUE,CAAmB,IAAKD,EACzCT,EAAS,IAAIU,CAAkB,GACjCV,EAAS,OAAOU,CAAkB,CAK5C,SAAWR,EAAK,OAAS,2BACd,oBAAkB,KAAKA,EAAK,IAAI,EAAG,CAC5C,MAAME,EAAWD,EAAK,OAAOE,GAAK,CAChC,KAAM,CAAE,KAAAC,EAAM,KAAAC,CAAK,EAAIF,EAGvB,OADEE,IAAS,2BAA2B,oBAAkB,KAAKD,CAAI,CAEnE,CAAC,EACD,SAAW,CAAE,SAAAE,CAAS,IAAKJ,EAEzB,SAAW,CAAE,SAAUK,CAAc,IAAKD,EACpCR,EAAS,IAAIS,CAAa,GAC5BT,EAAS,OAAOS,CAAa,CAIrC,EAEJ,CAAC,EAEI,CAAC,GAAGT,CAAQ,CACrB", "names": ["parser_exports", "__export", "parseSelector", "preprocess", "unescapeSelector", "walkAST", "__toCommonJS", "import_css_tree", "import_constant", "selector", "arr", "l", "item", "hexExists", "hex", "str", "low", "high", "deci", "postStr", "args", "index", "preHash", "postHash", "codePoint", "res", "ast", "e", "reg<PERSON><PERSON>", "lang", "range", "<PERSON><PERSON><PERSON><PERSON>", "escapedLang", "branches", "hasPseudoFunc", "node", "list", "itemList", "i", "name", "type", "children", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}