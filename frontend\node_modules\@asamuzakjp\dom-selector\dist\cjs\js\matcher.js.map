{"version": 3, "sources": ["../../../src/js/matcher.js"], "sourcesContent": ["/**\n * matcher.js\n */\n\n/* import */\nimport isCustomElementName from 'is-potential-custom-element-name';\nimport {\n  getDirectionality, isContentEditable, isInclusive, isInShadowTree,\n  isNamespaceDeclared, isPreceding, selectorToNodeProps\n} from './dom-util.js';\nimport {\n  generateCSS, parseSelector, unescapeSelector, walkAST\n} from './parser.js';\n\n/* constants */\nimport {\n  ALPHA_NUM, BIT_01, BIT_02, BIT_04, BIT_08, BIT_16, BIT_32, COMBINATOR,\n  DOCUMENT_FRAGMENT_NODE, DOCUMENT_NODE, ELEMENT_NODE, NOT_SUPPORTED_ERR,\n  REG_LOGICAL_PSEUDO, REG_SHADOW_HOST, SELECTOR_ATTR, SELECTOR_CLASS,\n  SELECTOR_ID, SELECTOR_PSEUDO_CLASS, SELECTOR_PSEUDO_ELEMENT, SELECTOR_TYPE,\n  SHOW_ALL, SHOW_DOCUMENT, SHOW_DOCUMENT_FRAGMENT, SHOW_ELEMENT, SYNTAX_ERR,\n  TEXT_NODE, TYPE_FROM, TYPE_TO\n} from './constant.js';\nconst DIR_NEXT = 'next';\nconst DIR_PREV = 'prev';\nconst TARGET_ALL = 'all';\nconst TARGET_FIRST = 'first';\nconst TARGET_LINEAL = 'lineal';\nconst TARGET_SELF = 'self';\n\n/**\n * Matcher\n * NOTE: #ast[i] corresponds to #nodes[i]\n * #ast: [\n *   {\n *     branch: branch[],\n *     dir: string|null,\n *     filtered: boolean,\n *     find: boolean\n *   },\n *   {\n *     branch: branch[],\n *     dir: string|null,\n *     filtered: boolean,\n *     find: boolean\n *   }\n * ]\n * #nodes: [\n *   Set([node{}, node{}]),\n *   Set([node{}, node{}, node{}])\n * ]\n * branch[]: [twig{}, twig{}]\n * twig{}: {\n *   combo: leaf{}|null,\n *   leaves: leaves[]\n * }\n * leaves[]: [leaf{}, leaf{}, leaf{}]\n * leaf{}: CSSTree AST object\n * node{}: Element node\n */\nexport class Matcher {\n  /* private fields */\n  #ast;\n  #bit;\n  #cache;\n  #document;\n  #finder;\n  #node;\n  #nodes;\n  #root;\n  #shadow;\n  #tree;\n  #warn;\n  #window;\n\n  /**\n   * construct\n   * @param {string} selector - CSS selector\n   * @param {object} node - Document, DocumentFragment, Element node\n   * @param {object} [opt] - options\n   * @param {boolean} [opt.warn] - console warn\n   */\n  constructor(selector, node, opt = {}) {\n    const { warn } = opt;\n    this.#bit = new Map([\n      [SELECTOR_PSEUDO_ELEMENT, BIT_01],\n      [SELECTOR_ID, BIT_02],\n      [SELECTOR_CLASS, BIT_04],\n      [SELECTOR_TYPE, BIT_08],\n      [SELECTOR_ATTR, BIT_16],\n      [SELECTOR_PSEUDO_CLASS, BIT_32]\n    ]);\n    this.#cache = new WeakMap();\n    this.#node = node;\n    [this.#window, this.#document, this.#root, this.#tree] = this._setup(node);\n    this.#shadow = isInShadowTree(node);\n    [this.#ast, this.#nodes] = this._correspond(selector);\n    this.#warn = !!warn;\n  }\n\n  /**\n   * handle error\n   * @param {Error} e - Error\n   * @throws Error\n   * @returns {void}\n   */\n  _onError(e) {\n    if ((e instanceof DOMException ||\n         e instanceof this.#window.DOMException) &&\n        e.name === NOT_SUPPORTED_ERR) {\n      if (this.#warn) {\n        console.warn(e.message);\n      }\n    } else if (e instanceof DOMException) {\n      throw new this.#window.DOMException(e.message, e.name);\n    } else if (e instanceof TypeError) {\n      throw new this.#window.TypeError(e.message);\n    } else {\n      throw e;\n    }\n  }\n\n  /**\n   * set up #window, #document, #root, #walker\n   * @param {object} node - Document, DocumentFragment, Element node\n   * @returns {Array.<object>} - array of #window, #document, #root, #walker\n   */\n  _setup(node) {\n    let document;\n    let root;\n    switch (node?.nodeType) {\n      case DOCUMENT_NODE: {\n        document = node;\n        root = node;\n        break;\n      }\n      case DOCUMENT_FRAGMENT_NODE: {\n        document = node.ownerDocument;\n        root = node;\n        break;\n      }\n      case ELEMENT_NODE: {\n        if (node.ownerDocument.contains(node)) {\n          document = node.ownerDocument;\n          root = node.ownerDocument;\n        } else {\n          let parent = node;\n          while (parent) {\n            if (parent.parentNode) {\n              parent = parent.parentNode;\n            } else {\n              break;\n            }\n          }\n          document = parent.ownerDocument;\n          root = parent;\n        }\n        break;\n      }\n      default: {\n        let msg;\n        if (node?.nodeName) {\n          msg = `Unexpected node ${node.nodeName}`;\n        } else {\n          const nodeType =\n            Object.prototype.toString.call(node).slice(TYPE_FROM, TYPE_TO);\n          msg = `Unexpected node ${nodeType}`;\n        }\n        throw new TypeError(msg);\n      }\n    }\n    const filter = SHOW_DOCUMENT | SHOW_DOCUMENT_FRAGMENT | SHOW_ELEMENT;\n    const walker = document.createTreeWalker(root, filter);\n    const window = document.defaultView;\n    return [\n      window,\n      document,\n      root,\n      walker\n    ];\n  }\n\n  /**\n   * sort AST leaves\n   * @param {Array.<object>} leaves - collection of AST leaves\n   * @returns {Array.<object>} - sorted leaves\n   */\n  _sortLeaves(leaves) {\n    const arr = [...leaves];\n    if (arr.length > 1) {\n      arr.sort((a, b) => {\n        const { type: typeA } = a;\n        const { type: typeB } = b;\n        const bitA = this.#bit.get(typeA);\n        const bitB = this.#bit.get(typeB);\n        let res;\n        if (bitA === bitB) {\n          res = 0;\n        } else if (bitA > bitB) {\n          res = 1;\n        } else {\n          res = -1;\n        }\n        return res;\n      });\n    }\n    return arr;\n  }\n\n  /**\n   * correspond #ast and #nodes\n   * @param {string} selector - CSS selector\n   * @returns {Array.<Array.<object|undefined>>} - array of #ast and #nodes\n   */\n  _correspond(selector) {\n    let cssAst;\n    try {\n      cssAst = parseSelector(selector);\n    } catch (e) {\n      this._onError(e);\n    }\n    const branches = walkAST(cssAst);\n    const ast = [];\n    const nodes = [];\n    let i = 0;\n    for (const [...items] of branches) {\n      const branch = [];\n      let item = items.shift();\n      if (item && item.type !== COMBINATOR) {\n        const leaves = new Set();\n        while (item) {\n          if (item.type === COMBINATOR) {\n            const [nextItem] = items;\n            if (nextItem.type === COMBINATOR) {\n              const msg = `Invalid combinator ${item.name}${nextItem.name}`;\n              throw new this.#window.DOMException(msg, SYNTAX_ERR);\n            }\n            branch.push({\n              combo: item,\n              leaves: this._sortLeaves(leaves)\n            });\n            leaves.clear();\n          } else if (item) {\n            leaves.add(item);\n          }\n          if (items.length) {\n            item = items.shift();\n          } else {\n            branch.push({\n              combo: null,\n              leaves: this._sortLeaves(leaves)\n            });\n            leaves.clear();\n            break;\n          }\n        }\n      }\n      ast.push({\n        branch,\n        dir: null,\n        filtered: false,\n        find: false\n      });\n      nodes[i] = new Set();\n      i++;\n    }\n    return [\n      ast,\n      nodes\n    ];\n  }\n\n  /**\n   * traverse tree walker\n   * @param {object} [node] - Element node\n   * @param {object} [walker] - tree walker\n   * @returns {?object} - current node\n   */\n  _traverse(node = {}, walker = this.#tree) {\n    let current;\n    let refNode = walker.currentNode;\n    if (node.nodeType === ELEMENT_NODE && refNode === node) {\n      current = refNode;\n    } else {\n      if (refNode !== walker.root) {\n        while (refNode) {\n          if (refNode === walker.root ||\n              (node.nodeType === ELEMENT_NODE && refNode === node)) {\n            break;\n          }\n          refNode = walker.parentNode();\n        }\n      }\n      if (node.nodeType === ELEMENT_NODE) {\n        while (refNode) {\n          if (refNode === node) {\n            current = refNode;\n            break;\n          }\n          refNode = walker.nextNode();\n        }\n      } else {\n        current = refNode;\n      }\n    }\n    return current ?? null;\n  }\n\n  /**\n   * collect nth child\n   * @param {object} anb - An+B options\n   * @param {number} anb.a - a\n   * @param {number} anb.b - b\n   * @param {boolean} [anb.reverse] - reverse order\n   * @param {object} [anb.selector] - AST\n   * @param {object} node - Element node\n   * @returns {Set.<object>} - collection of matched nodes\n   */\n  _collectNthChild(anb, node) {\n    const { a, b, reverse, selector } = anb;\n    const { parentNode } = node;\n    let matched = new Set();\n    let selectorBranches;\n    if (selector) {\n      if (this.#cache.has(selector)) {\n        selectorBranches = this.#cache.get(selector);\n      } else {\n        selectorBranches = walkAST(selector);\n        this.#cache.set(selector, selectorBranches);\n      }\n    }\n    if (parentNode) {\n      const filter = SHOW_DOCUMENT | SHOW_DOCUMENT_FRAGMENT | SHOW_ELEMENT;\n      const walker = this.#document.createTreeWalker(parentNode, filter);\n      let l = 0;\n      let refNode = walker.firstChild();\n      while (refNode) {\n        l++;\n        refNode = walker.nextSibling();\n      }\n      refNode = this._traverse(parentNode, walker);\n      const selectorNodes = new Set();\n      if (selectorBranches) {\n        refNode = this._traverse(parentNode, walker);\n        refNode = walker.firstChild();\n        while (refNode) {\n          let bool;\n          for (const leaves of selectorBranches) {\n            bool = this._matchLeaves(leaves, refNode);\n            if (!bool) {\n              break;\n            }\n          }\n          if (bool) {\n            selectorNodes.add(refNode);\n          }\n          refNode = walker.nextSibling();\n        }\n      }\n      // :first-child, :last-child, :nth-child(b of S), :nth-last-child(b of S)\n      if (a === 0) {\n        if (b > 0 && b <= l) {\n          if (selectorNodes.size) {\n            let i = 0;\n            refNode = this._traverse(parentNode, walker);\n            if (reverse) {\n              refNode = walker.lastChild();\n            } else {\n              refNode = walker.firstChild();\n            }\n            while (refNode) {\n              if (selectorNodes.has(refNode)) {\n                if (i === b - 1) {\n                  matched.add(refNode);\n                  break;\n                }\n                i++;\n              }\n              if (reverse) {\n                refNode = walker.previousSibling();\n              } else {\n                refNode = walker.nextSibling();\n              }\n            }\n          } else if (!selector) {\n            let i = 0;\n            refNode = this._traverse(parentNode, walker);\n            if (reverse) {\n              refNode = walker.lastChild();\n            } else {\n              refNode = walker.firstChild();\n            }\n            while (refNode) {\n              if (i === b - 1) {\n                matched.add(refNode);\n                break;\n              }\n              if (reverse) {\n                refNode = walker.previousSibling();\n              } else {\n                refNode = walker.nextSibling();\n              }\n              i++;\n            }\n          }\n        }\n      // :nth-child()\n      } else {\n        let nth = b - 1;\n        if (a > 0) {\n          while (nth < 0) {\n            nth += a;\n          }\n        }\n        if (nth >= 0 && nth < l) {\n          let i = 0;\n          let j = a > 0 ? 0 : b - 1;\n          refNode = this._traverse(parentNode, walker);\n          if (reverse) {\n            refNode = walker.lastChild();\n          } else {\n            refNode = walker.firstChild();\n          }\n          while (refNode) {\n            if (refNode && nth >= 0 && nth < l) {\n              if (selectorNodes.size) {\n                if (selectorNodes.has(refNode)) {\n                  if (j === nth) {\n                    matched.add(refNode);\n                    nth += a;\n                  }\n                  if (a > 0) {\n                    j++;\n                  } else {\n                    j--;\n                  }\n                }\n              } else if (i === nth) {\n                if (!selector) {\n                  matched.add(refNode);\n                }\n                nth += a;\n              }\n              if (reverse) {\n                refNode = walker.previousSibling();\n              } else {\n                refNode = walker.nextSibling();\n              }\n              i++;\n            } else {\n              break;\n            }\n          }\n        }\n      }\n      if (reverse && matched.size > 1) {\n        const m = [...matched];\n        matched = new Set(m.reverse());\n      }\n    } else if (node === this.#root && this.#root.nodeType === ELEMENT_NODE &&\n               (a + b) === 1) {\n      if (selectorBranches) {\n        let bool;\n        for (const leaves of selectorBranches) {\n          bool = this._matchLeaves(leaves, node);\n          if (bool) {\n            break;\n          }\n        }\n        if (bool) {\n          matched.add(node);\n        }\n      } else {\n        matched.add(node);\n      }\n    }\n    return matched;\n  }\n\n  /**\n   * collect nth of type\n   * @param {object} anb - An+B options\n   * @param {number} anb.a - a\n   * @param {number} anb.b - b\n   * @param {boolean} [anb.reverse] - reverse order\n   * @param {object} node - Element node\n   * @returns {Set.<object>} - collection of matched nodes\n   */\n  _collectNthOfType(anb, node) {\n    const { a, b, reverse } = anb;\n    const { localName, parentNode, prefix } = node;\n    let matched = new Set();\n    if (parentNode) {\n      const filter = SHOW_DOCUMENT | SHOW_DOCUMENT_FRAGMENT | SHOW_ELEMENT;\n      const walker = this.#document.createTreeWalker(parentNode, filter);\n      let l = 0;\n      let refNode = walker.firstChild();\n      while (refNode) {\n        l++;\n        refNode = walker.nextSibling();\n      }\n      // :first-of-type, :last-of-type\n      if (a === 0) {\n        if (b > 0 && b <= l) {\n          let j = 0;\n          refNode = this._traverse(parentNode, walker);\n          if (reverse) {\n            refNode = walker.lastChild();\n          } else {\n            refNode = walker.firstChild();\n          }\n          while (refNode) {\n            const { localName: itemLocalName, prefix: itemPrefix } = refNode;\n            if (itemLocalName === localName && itemPrefix === prefix) {\n              if (j === b - 1) {\n                matched.add(refNode);\n                break;\n              }\n              j++;\n            }\n            if (reverse) {\n              refNode = walker.previousSibling();\n            } else {\n              refNode = walker.nextSibling();\n            }\n          }\n        }\n      // :nth-of-type()\n      } else {\n        let nth = b - 1;\n        if (a > 0) {\n          while (nth < 0) {\n            nth += a;\n          }\n        }\n        if (nth >= 0 && nth < l) {\n          let j = a > 0 ? 0 : b - 1;\n          refNode = this._traverse(parentNode, walker);\n          if (reverse) {\n            refNode = walker.lastChild();\n          } else {\n            refNode = walker.firstChild();\n          }\n          while (refNode) {\n            const { localName: itemLocalName, prefix: itemPrefix } = refNode;\n            if (itemLocalName === localName && itemPrefix === prefix) {\n              if (j === nth) {\n                matched.add(refNode);\n                nth += a;\n              }\n              if (nth < 0 || nth >= l) {\n                break;\n              } else if (a > 0) {\n                j++;\n              } else {\n                j--;\n              }\n            }\n            if (reverse) {\n              refNode = walker.previousSibling();\n            } else {\n              refNode = walker.nextSibling();\n            }\n          }\n        }\n      }\n      if (reverse && matched.size > 1) {\n        const m = [...matched];\n        matched = new Set(m.reverse());\n      }\n    } else if (node === this.#root && this.#root.nodeType === ELEMENT_NODE &&\n               (a + b) === 1) {\n      matched.add(node);\n    }\n    return matched;\n  }\n\n  /**\n   * match An+B\n   * @param {object} ast - AST\n   * @param {object} node - Element node\n   * @param {string} nthName - nth pseudo-class name\n   * @returns {Set.<object>} - collection of matched nodes\n   */\n  _matchAnPlusB(ast, node, nthName) {\n    const {\n      nth: {\n        a,\n        b,\n        name: nthIdentName\n      },\n      selector\n    } = ast;\n    const identName = unescapeSelector(nthIdentName);\n    const anbMap = new Map();\n    if (identName) {\n      if (identName === 'even') {\n        anbMap.set('a', 2);\n        anbMap.set('b', 0);\n      } else if (identName === 'odd') {\n        anbMap.set('a', 2);\n        anbMap.set('b', 1);\n      }\n      if (nthName.indexOf('last') > -1) {\n        anbMap.set('reverse', true);\n      }\n    } else {\n      if (typeof a === 'string' && /-?\\d+/.test(a)) {\n        anbMap.set('a', a * 1);\n      } else {\n        anbMap.set('a', 0);\n      }\n      if (typeof b === 'string' && /-?\\d+/.test(b)) {\n        anbMap.set('b', b * 1);\n      } else {\n        anbMap.set('b', 0);\n      }\n      if (nthName.indexOf('last') > -1) {\n        anbMap.set('reverse', true);\n      }\n    }\n    let matched = new Set();\n    if (anbMap.has('a') && anbMap.has('b')) {\n      if (/^nth-(?:last-)?child$/.test(nthName)) {\n        if (selector) {\n          anbMap.set('selector', selector);\n        }\n        const anb = Object.fromEntries(anbMap);\n        const nodes = this._collectNthChild(anb, node);\n        if (nodes.size) {\n          matched = nodes;\n        }\n      } else if (/^nth-(?:last-)?of-type$/.test(nthName)) {\n        const anb = Object.fromEntries(anbMap);\n        const nodes = this._collectNthOfType(anb, node);\n        if (nodes.size) {\n          matched = nodes;\n        }\n      }\n    }\n    return matched;\n  }\n\n  /**\n   * match pseudo element selector\n   * @param {string} astName - AST name\n   * @param {object} [opt] - options\n   * @param {boolean} [opt.forgive] - is forgiving selector list\n   * @throws {DOMException}\n   * @returns {void}\n   */\n  _matchPseudoElementSelector(astName, opt = {}) {\n    const { forgive } = opt;\n    switch (astName) {\n      case 'after':\n      case 'backdrop':\n      case 'before':\n      case 'cue':\n      case 'cue-region':\n      case 'first-letter':\n      case 'first-line':\n      case 'file-selector-button':\n      case 'marker':\n      case 'placeholder':\n      case 'selection':\n      case 'target-text': {\n        if (this.#warn) {\n          const msg = `Unsupported pseudo-element ::${astName}`;\n          throw new DOMException(msg, NOT_SUPPORTED_ERR);\n        }\n        break;\n      }\n      case 'part':\n      case 'slotted': {\n        if (this.#warn) {\n          const msg = `Unsupported pseudo-element ::${astName}()`;\n          throw new DOMException(msg, NOT_SUPPORTED_ERR);\n        }\n        break;\n      }\n      default: {\n        if (astName.startsWith('-webkit-')) {\n          if (this.#warn) {\n            const msg = `Unsupported pseudo-element ::${astName}`;\n            throw new DOMException(msg, NOT_SUPPORTED_ERR);\n          }\n        } else if (!forgive) {\n          const msg = `Unknown pseudo-element ::${astName}`;\n          throw new DOMException(msg, SYNTAX_ERR);\n        }\n      }\n    }\n  }\n\n  /**\n   * match directionality pseudo-class - :dir()\n   * @param {object} ast - AST\n   * @param {object} node - Element node\n   * @returns {?object} - matched node\n   */\n  _matchDirectionPseudoClass(ast, node) {\n    const astName = unescapeSelector(ast.name);\n    const dir = getDirectionality(node);\n    let res;\n    if (astName === dir) {\n      res = node;\n    }\n    return res ?? null;\n  }\n\n  /**\n   * match language pseudo-class - :lang()\n   * @see https://datatracker.ietf.org/doc/html/rfc4647#section-3.3.1\n   * @param {object} ast - AST\n   * @param {object} node - Element node\n   * @returns {?object} - matched node\n   */\n  _matchLanguagePseudoClass(ast, node) {\n    const astName = unescapeSelector(ast.name);\n    let res;\n    if (astName === '*') {\n      if (node.hasAttribute('lang')) {\n        if (node.getAttribute('lang')) {\n          res = node;\n        }\n      } else {\n        let parent = node.parentNode;\n        while (parent) {\n          if (parent.nodeType === ELEMENT_NODE) {\n            if (parent.hasAttribute('lang')) {\n              if (parent.getAttribute('lang')) {\n                res = node;\n              }\n              break;\n            }\n            parent = parent.parentNode;\n          } else {\n            break;\n          }\n        }\n      }\n    } else if (astName) {\n      const langPart = `(?:-${ALPHA_NUM})*`;\n      const regLang = new RegExp(`^(?:\\\\*-)?${ALPHA_NUM}${langPart}$`, 'i');\n      if (regLang.test(astName)) {\n        let regExtendedLang;\n        if (astName.indexOf('-') > -1) {\n          const [langMain, langSub, ...langRest] = astName.split('-');\n          let extendedMain;\n          if (langMain === '*') {\n            extendedMain = `${ALPHA_NUM}${langPart}`;\n          } else {\n            extendedMain = `${langMain}${langPart}`;\n          }\n          const extendedSub = `-${langSub}${langPart}`;\n          const len = langRest.length;\n          let extendedRest = '';\n          if (len) {\n            for (let i = 0; i < len; i++) {\n              extendedRest += `-${langRest[i]}${langPart}`;\n            }\n          }\n          regExtendedLang =\n            new RegExp(`^${extendedMain}${extendedSub}${extendedRest}$`, 'i');\n        } else {\n          regExtendedLang = new RegExp(`^${astName}${langPart}$`, 'i');\n        }\n        if (node.hasAttribute('lang')) {\n          if (regExtendedLang.test(node.getAttribute('lang'))) {\n            res = node;\n          }\n        } else {\n          let parent = node.parentNode;\n          while (parent) {\n            if (parent.nodeType === ELEMENT_NODE) {\n              if (parent.hasAttribute('lang')) {\n                const value = parent.getAttribute('lang');\n                if (regExtendedLang.test(value)) {\n                  res = node;\n                }\n                break;\n              }\n              parent = parent.parentNode;\n            } else {\n              break;\n            }\n          }\n        }\n      }\n    }\n    return res ?? null;\n  }\n\n  /**\n   * match :has() pseudo-class function\n   * @param {Array.<object>} leaves - AST leaves\n   * @param {object} node - Element node\n   * @returns {boolean} - result\n   */\n  _matchHasPseudoFunc(leaves, node) {\n    let bool;\n    if (Array.isArray(leaves) && leaves.length) {\n      const [leaf] = leaves;\n      const { type: leafType } = leaf;\n      let combo;\n      if (leafType === COMBINATOR) {\n        combo = leaves.shift();\n      } else {\n        combo = {\n          name: ' ',\n          type: COMBINATOR\n        };\n      }\n      const twigLeaves = [];\n      while (leaves.length) {\n        const [item] = leaves;\n        const { type: itemType } = item;\n        if (itemType === COMBINATOR) {\n          break;\n        } else {\n          twigLeaves.push(leaves.shift());\n        }\n      }\n      const twig = {\n        combo,\n        leaves: twigLeaves\n      };\n      const nodes = this._matchCombinator(twig, node, {\n        dir: DIR_NEXT\n      });\n      if (nodes.size) {\n        if (leaves.length) {\n          for (const nextNode of nodes) {\n            bool =\n              this._matchHasPseudoFunc(Object.assign([], leaves), nextNode);\n            if (bool) {\n              break;\n            }\n          }\n        } else {\n          bool = true;\n        }\n      }\n    }\n    return !!bool;\n  }\n\n  /**\n   * match logical pseudo-class functions - :has(), :is(), :not(), :where()\n   * @param {object} astData - AST data\n   * @param {object} node - Element node\n   * @returns {?object} - matched node\n   */\n  _matchLogicalPseudoFunc(astData, node) {\n    const {\n      astName = '', branches = [], selector = '', twigBranches = []\n    } = astData;\n    let res;\n    if (astName === 'has') {\n      if (selector.includes(':has(')) {\n        res = null;\n      } else {\n        let bool;\n        for (const leaves of branches) {\n          bool = this._matchHasPseudoFunc(Object.assign([], leaves), node);\n          if (bool) {\n            break;\n          }\n        }\n        if (bool) {\n          res = node;\n        }\n      }\n    } else {\n      const forgive = /^(?:is|where)$/.test(astName);\n      const l = twigBranches.length;\n      let bool;\n      for (let i = 0; i < l; i++) {\n        const branch = twigBranches[i];\n        const lastIndex = branch.length - 1;\n        const { leaves } = branch[lastIndex];\n        bool = this._matchLeaves(leaves, node, { forgive });\n        if (bool && lastIndex > 0) {\n          let nextNodes = new Set([node]);\n          for (let j = lastIndex - 1; j >= 0; j--) {\n            const twig = branch[j];\n            const arr = [];\n            for (const nextNode of nextNodes) {\n              const m = this._matchCombinator(twig, nextNode, {\n                forgive,\n                dir: DIR_PREV\n              });\n              if (m.size) {\n                arr.push(...m);\n              }\n            }\n            if (arr.length) {\n              if (j === 0) {\n                bool = true;\n              } else {\n                nextNodes = new Set(arr);\n              }\n            } else {\n              bool = false;\n              break;\n            }\n          }\n        }\n        if (bool) {\n          break;\n        }\n      }\n      if (astName === 'not') {\n        if (!bool) {\n          res = node;\n        }\n      } else if (bool) {\n        res = node;\n      }\n    }\n    return res ?? null;\n  }\n\n  /**\n   * match pseudo-class selector\n   * @see https://html.spec.whatwg.org/#pseudo-classes\n   * @param {object} ast - AST\n   * @param {object} node - Element node\n   * @param {object} [opt] - options\n   * @param {boolean} [opt.forgive] - is forgiving selector list\n   * @returns {Set.<object>} - collection of matched nodes\n   */\n  _matchPseudoClassSelector(ast, node, opt = {}) {\n    const { children: astChildren } = ast;\n    const { localName, parentNode } = node;\n    const { forgive } = opt;\n    const astName = unescapeSelector(ast.name);\n    let matched = new Set();\n    // :has(), :is(), :not(), :where()\n    if (REG_LOGICAL_PSEUDO.test(astName)) {\n      let astData;\n      if (this.#cache.has(ast)) {\n        astData = this.#cache.get(ast);\n      } else {\n        const branches = walkAST(ast);\n        const selectors = [];\n        const twigBranches = [];\n        for (const [...leaves] of branches) {\n          for (const leaf of leaves) {\n            const css = generateCSS(leaf);\n            selectors.push(css);\n          }\n          const branch = [];\n          const leavesSet = new Set();\n          let item = leaves.shift();\n          while (item) {\n            if (item.type === COMBINATOR) {\n              branch.push({\n                combo: item,\n                leaves: [...leavesSet]\n              });\n              leavesSet.clear();\n            } else if (item) {\n              leavesSet.add(item);\n            }\n            if (leaves.length) {\n              item = leaves.shift();\n            } else {\n              branch.push({\n                combo: null,\n                leaves: [...leavesSet]\n              });\n              leavesSet.clear();\n              break;\n            }\n          }\n          twigBranches.push(branch);\n        }\n        astData = {\n          astName,\n          branches,\n          twigBranches,\n          selector: selectors.join(',')\n        };\n        this.#cache.set(ast, astData);\n      }\n      const res = this._matchLogicalPseudoFunc(astData, node);\n      if (res) {\n        matched.add(res);\n      }\n    } else if (Array.isArray(astChildren)) {\n      const [branch] = astChildren;\n      // :nth-child(), :nth-last-child(), nth-of-type(), :nth-last-of-type()\n      if (/^nth-(?:last-)?(?:child|of-type)$/.test(astName)) {\n        const nodes = this._matchAnPlusB(branch, node, astName);\n        if (nodes.size) {\n          matched = nodes;\n        }\n      // :dir()\n      } else if (astName === 'dir') {\n        const res = this._matchDirectionPseudoClass(branch, node);\n        if (res) {\n          matched.add(res);\n        }\n      // :lang()\n      } else if (astName === 'lang') {\n        const res = this._matchLanguagePseudoClass(branch, node);\n        if (res) {\n          matched.add(res);\n        }\n      } else {\n        switch (astName) {\n          case 'current':\n          case 'nth-col':\n          case 'nth-last-col': {\n            if (this.#warn) {\n              const msg = `Unsupported pseudo-class :${astName}()`;\n              throw new DOMException(msg, NOT_SUPPORTED_ERR);\n            }\n            break;\n          }\n          case 'host':\n          case 'host-context': {\n            // ignore\n            break;\n          }\n          default: {\n            if (!forgive) {\n              const msg = `Unknown pseudo-class :${astName}()`;\n              throw new DOMException(msg, SYNTAX_ERR);\n            }\n          }\n        }\n      }\n    } else {\n      const regAnchor = /^a(?:rea)?$/;\n      const regFormCtrl =\n        /^(?:(?:fieldse|inpu|selec)t|button|opt(?:group|ion)|textarea)$/;\n      const regFormValidity = /^(?:(?:inpu|selec)t|button|form|textarea)$/;\n      const regInteract = /^d(?:etails|ialog)$/;\n      const regTypeCheck = /^(?:checkbox|radio)$/;\n      const regTypeDate = /^(?:date(?:time-local)?|month|time|week)$/;\n      const regTypeRange =\n        /(?:(?:rang|tim)e|date(?:time-local)?|month|number|week)$/;\n      const regTypeText = /^(?:(?:emai|te|ur)l|number|password|search|text)$/;\n      switch (astName) {\n        case 'any-link':\n        case 'link': {\n          if (regAnchor.test(localName) && node.hasAttribute('href')) {\n            matched.add(node);\n          }\n          break;\n        }\n        case 'local-link': {\n          if (regAnchor.test(localName) && node.hasAttribute('href')) {\n            const { href, origin, pathname } = new URL(this.#document.URL);\n            const attrURL = new URL(node.getAttribute('href'), href);\n            if (attrURL.origin === origin && attrURL.pathname === pathname) {\n              matched.add(node);\n            }\n          }\n          break;\n        }\n        case 'visited': {\n          // prevent fingerprinting\n          break;\n        }\n        case 'target': {\n          const { hash } = new URL(this.#document.URL);\n          if (node.id && hash === `#${node.id}` &&\n              this.#document.contains(node)) {\n            matched.add(node);\n          }\n          break;\n        }\n        case 'target-within': {\n          const { hash } = new URL(this.#document.URL);\n          if (hash) {\n            const id = hash.replace(/^#/, '');\n            let current = this.#document.getElementById(id);\n            while (current) {\n              if (current === node) {\n                matched.add(node);\n                break;\n              }\n              current = current.parentNode;\n            }\n          }\n          break;\n        }\n        case 'scope': {\n          if (this.#node.nodeType === ELEMENT_NODE) {\n            if (node === this.#node) {\n              matched.add(node);\n            }\n          } else if (node === this.#document.documentElement) {\n            matched.add(node);\n          }\n          break;\n        }\n        case 'focus': {\n          if (node === this.#document.activeElement) {\n            matched.add(node);\n          }\n          break;\n        }\n        case 'focus-within': {\n          let current = this.#document.activeElement;\n          while (current) {\n            if (current === node) {\n              matched.add(node);\n              break;\n            }\n            current = current.parentNode;\n          }\n          break;\n        }\n        case 'open': {\n          if (regInteract.test(localName) && node.hasAttribute('open')) {\n            matched.add(node);\n          }\n          break;\n        }\n        case 'closed': {\n          if (regInteract.test(localName) && !node.hasAttribute('open')) {\n            matched.add(node);\n          }\n          break;\n        }\n        case 'disabled': {\n          if (regFormCtrl.test(localName) || isCustomElementName(localName)) {\n            if (node.disabled || node.hasAttribute('disabled')) {\n              matched.add(node);\n            } else {\n              let parent = parentNode;\n              while (parent) {\n                if (parent.localName === 'fieldset') {\n                  break;\n                }\n                parent = parent.parentNode;\n              }\n              if (parent && parentNode.localName !== 'legend' &&\n                  parent.hasAttribute('disabled')) {\n                matched.add(node);\n              }\n            }\n          }\n          break;\n        }\n        case 'enabled': {\n          if ((regFormCtrl.test(localName) || isCustomElementName(localName)) &&\n              !(node.disabled && node.hasAttribute('disabled'))) {\n            matched.add(node);\n          }\n          break;\n        }\n        case 'read-only': {\n          switch (localName) {\n            case 'textarea': {\n              if (node.readonly || node.hasAttribute('readonly') ||\n                  node.disabled || node.hasAttribute('disabled')) {\n                matched.add(node);\n              }\n              break;\n            }\n            case 'input': {\n              if ((!node.type || regTypeDate.test(node.type) ||\n                   regTypeText.test(node.type)) &&\n                  (node.readonly || node.hasAttribute('readonly') ||\n                   node.disabled || node.hasAttribute('disabled'))) {\n                matched.add(node);\n              }\n              break;\n            }\n            default: {\n              if (!isContentEditable(node)) {\n                matched.add(node);\n              }\n            }\n          }\n          break;\n        }\n        case 'read-write': {\n          switch (localName) {\n            case 'textarea': {\n              if (!(node.readonly || node.hasAttribute('readonly') ||\n                    node.disabled || node.hasAttribute('disabled'))) {\n                matched.add(node);\n              }\n              break;\n            }\n            case 'input': {\n              if ((!node.type || regTypeDate.test(node.type) ||\n                   regTypeText.test(node.type)) &&\n                  !(node.readonly || node.hasAttribute('readonly') ||\n                    node.disabled || node.hasAttribute('disabled'))) {\n                matched.add(node);\n              }\n              break;\n            }\n            default: {\n              if (isContentEditable(node)) {\n                matched.add(node);\n              }\n            }\n          }\n          break;\n        }\n        case 'placeholder-shown': {\n          let targetNode;\n          if (localName === 'textarea') {\n            targetNode = node;\n          } else if (localName === 'input') {\n            if (node.hasAttribute('type')) {\n              if (regTypeText.test(node.getAttribute('type'))) {\n                targetNode = node;\n              }\n            } else {\n              targetNode = node;\n            }\n          }\n          if (targetNode && node.value === '' &&\n              node.hasAttribute('placeholder') &&\n              node.getAttribute('placeholder').trim().length) {\n            matched.add(node);\n          }\n          break;\n        }\n        case 'checked': {\n          if ((node.checked && localName === 'input' &&\n               node.hasAttribute('type') &&\n               regTypeCheck.test(node.getAttribute('type'))) ||\n              (node.selected && localName === 'option')) {\n            matched.add(node);\n          }\n          break;\n        }\n        case 'indeterminate': {\n          if ((node.indeterminate && localName === 'input' &&\n               node.type === 'checkbox') ||\n              (localName === 'progress' && !node.hasAttribute('value'))) {\n            matched.add(node);\n          } else if (localName === 'input' && node.type === 'radio' &&\n                     !node.hasAttribute('checked')) {\n            const nodeName = node.name;\n            let parent = node.parentNode;\n            while (parent) {\n              if (parent.localName === 'form') {\n                break;\n              }\n              parent = parent.parentNode;\n            }\n            if (!parent) {\n              parent = this.#document.documentElement;\n            }\n            let checked;\n            const nodes = [].slice.call(parent.getElementsByTagName('input'));\n            for (const item of nodes) {\n              if (item.getAttribute('type') === 'radio') {\n                if (nodeName) {\n                  if (item.getAttribute('name') === nodeName) {\n                    checked = !!item.checked;\n                  }\n                } else if (!item.hasAttribute('name')) {\n                  checked = !!item.checked;\n                }\n                if (checked) {\n                  break;\n                }\n              }\n            }\n            if (!checked) {\n              matched.add(node);\n            }\n          }\n          break;\n        }\n        case 'default': {\n          const regTypeReset = /^(?:button|reset)$/;\n          const regTypeSubmit = /^(?:image|submit)$/;\n          // button[type=\"submit\"], input[type=\"submit\"], input[type=\"image\"]\n          if ((localName === 'button' &&\n               !(node.hasAttribute('type') &&\n                 regTypeReset.test(node.getAttribute('type')))) ||\n              (localName === 'input' && node.hasAttribute('type') &&\n               regTypeSubmit.test(node.getAttribute('type')))) {\n            let form = node.parentNode;\n            while (form) {\n              if (form.localName === 'form') {\n                break;\n              }\n              form = form.parentNode;\n            }\n            if (form) {\n              const walker =\n                this.#document.createTreeWalker(form, SHOW_ELEMENT);\n              let nextNode = walker.firstChild();\n              while (nextNode) {\n                const nodeName = nextNode.localName;\n                let m;\n                if (nodeName === 'button') {\n                  m = !(nextNode.hasAttribute('type') &&\n                    regTypeReset.test(nextNode.getAttribute('type')));\n                } else if (nodeName === 'input') {\n                  m = nextNode.hasAttribute('type') &&\n                    regTypeSubmit.test(nextNode.getAttribute('type'));\n                }\n                if (m) {\n                  if (nextNode === node) {\n                    matched.add(node);\n                  }\n                  break;\n                }\n                nextNode = walker.nextNode();\n              }\n            }\n          // input[type=\"checkbox\"], input[type=\"radio\"]\n          } else if (localName === 'input' && node.hasAttribute('type') &&\n                     regTypeCheck.test(node.getAttribute('type')) &&\n                     (node.checked || node.hasAttribute('checked'))) {\n            matched.add(node);\n          // option\n          } else if (localName === 'option') {\n            let isMultiple = false;\n            let parent = parentNode;\n            while (parent) {\n              if (parent.localName === 'datalist') {\n                break;\n              } else if (parent.localName === 'select') {\n                if (parent.multiple || parent.hasAttribute('multiple')) {\n                  isMultiple = true;\n                }\n                break;\n              }\n              parent = parent.parentNode;\n            }\n            if (isMultiple) {\n              if (node.selected || node.hasAttribute('selected')) {\n                matched.add(node);\n              }\n            } else {\n              const defaultOpt = new Set();\n              const walker =\n                this.#document.createTreeWalker(parentNode, SHOW_ELEMENT);\n              let refNode = walker.firstChild();\n              while (refNode) {\n                if (refNode.selected || refNode.hasAttribute('selected')) {\n                  defaultOpt.add(refNode);\n                  break;\n                }\n                refNode = walker.nextSibling();\n              }\n              if (defaultOpt.size) {\n                if (defaultOpt.has(node)) {\n                  matched.add(node);\n                }\n              }\n            }\n          }\n          break;\n        }\n        case 'valid': {\n          if (regFormValidity.test(localName)) {\n            if (node.checkValidity()) {\n              matched.add(node);\n            }\n          } else if (localName === 'fieldset') {\n            let bool;\n            const walker = this.#document.createTreeWalker(node, SHOW_ELEMENT);\n            let refNode = walker.firstChild();\n            while (refNode) {\n              if (regFormValidity.test(refNode.localName)) {\n                bool = refNode.checkValidity();\n                if (!bool) {\n                  break;\n                }\n              }\n              refNode = walker.nextNode();\n            }\n            if (bool) {\n              matched.add(node);\n            }\n          }\n          break;\n        }\n        case 'invalid': {\n          if (regFormValidity.test(localName)) {\n            if (!node.checkValidity()) {\n              matched.add(node);\n            }\n          } else if (localName === 'fieldset') {\n            let bool;\n            const walker = this.#document.createTreeWalker(node, SHOW_ELEMENT);\n            let refNode = walker.firstChild();\n            while (refNode) {\n              if (regFormValidity.test(refNode.localName)) {\n                bool = refNode.checkValidity();\n                if (!bool) {\n                  break;\n                }\n              }\n              refNode = walker.nextNode();\n            }\n            if (!bool) {\n              matched.add(node);\n            }\n          }\n          break;\n        }\n        case 'in-range': {\n          if (localName === 'input' &&\n              !(node.readonly || node.hasAttribute('readonly')) &&\n              !(node.disabled || node.hasAttribute('disabled')) &&\n              node.hasAttribute('type') &&\n              regTypeRange.test(node.getAttribute('type')) &&\n              !(node.validity.rangeUnderflow ||\n                node.validity.rangeOverflow) &&\n              (node.hasAttribute('min') || node.hasAttribute('max') ||\n               node.getAttribute('type') === 'range')) {\n            matched.add(node);\n          }\n          break;\n        }\n        case 'out-of-range': {\n          if (localName === 'input' &&\n              !(node.readonly || node.hasAttribute('readonly')) &&\n              !(node.disabled || node.hasAttribute('disabled')) &&\n              node.hasAttribute('type') &&\n              regTypeRange.test(node.getAttribute('type')) &&\n              (node.validity.rangeUnderflow || node.validity.rangeOverflow)) {\n            matched.add(node);\n          }\n          break;\n        }\n        case 'required': {\n          let targetNode;\n          if (/^(?:select|textarea)$/.test(localName)) {\n            targetNode = node;\n          } else if (localName === 'input') {\n            if (node.hasAttribute('type')) {\n              const inputType = node.getAttribute('type');\n              if (inputType === 'file' || regTypeCheck.test(inputType) ||\n                  regTypeDate.test(inputType) || regTypeText.test(inputType)) {\n                targetNode = node;\n              }\n            } else {\n              targetNode = node;\n            }\n          }\n          if (targetNode &&\n              (node.required || node.hasAttribute('required'))) {\n            matched.add(node);\n          }\n          break;\n        }\n        case 'optional': {\n          let targetNode;\n          if (/^(?:select|textarea)$/.test(localName)) {\n            targetNode = node;\n          } else if (localName === 'input') {\n            if (node.hasAttribute('type')) {\n              const inputType = node.getAttribute('type');\n              if (inputType === 'file' || regTypeCheck.test(inputType) ||\n                  regTypeDate.test(inputType) || regTypeText.test(inputType)) {\n                targetNode = node;\n              }\n            } else {\n              targetNode = node;\n            }\n          }\n          if (targetNode &&\n              !(node.required || node.hasAttribute('required'))) {\n            matched.add(node);\n          }\n          break;\n        }\n        case 'root': {\n          if (node === this.#document.documentElement) {\n            matched.add(node);\n          }\n          break;\n        }\n        case 'empty': {\n          if (node.hasChildNodes()) {\n            let bool;\n            const walker = this.#document.createTreeWalker(node, SHOW_ALL);\n            let refNode = walker.firstChild();\n            while (refNode) {\n              bool = refNode.nodeType !== ELEMENT_NODE &&\n                refNode.nodeType !== TEXT_NODE;\n              if (!bool) {\n                break;\n              }\n              refNode = walker.nextSibling();\n            }\n            if (bool) {\n              matched.add(node);\n            }\n          } else {\n            matched.add(node);\n          }\n          break;\n        }\n        case 'first-child': {\n          if ((parentNode && node === parentNode.firstElementChild) ||\n              (node === this.#root && this.#root.nodeType === ELEMENT_NODE)) {\n            matched.add(node);\n          }\n          break;\n        }\n        case 'last-child': {\n          if ((parentNode && node === parentNode.lastElementChild) ||\n              (node === this.#root && this.#root.nodeType === ELEMENT_NODE)) {\n            matched.add(node);\n          }\n          break;\n        }\n        case 'only-child': {\n          if ((parentNode &&\n               node === parentNode.firstElementChild &&\n               node === parentNode.lastElementChild) ||\n              (node === this.#root && this.#root.nodeType === ELEMENT_NODE)) {\n            matched.add(node);\n          }\n          break;\n        }\n        case 'first-of-type': {\n          if (parentNode) {\n            const [node1] = this._collectNthOfType({\n              a: 0,\n              b: 1\n            }, node);\n            if (node1) {\n              matched.add(node1);\n            }\n          } else if (node === this.#root &&\n                     this.#root.nodeType === ELEMENT_NODE) {\n            matched.add(node);\n          }\n          break;\n        }\n        case 'last-of-type': {\n          if (parentNode) {\n            const [node1] = this._collectNthOfType({\n              a: 0,\n              b: 1,\n              reverse: true\n            }, node);\n            if (node1) {\n              matched.add(node1);\n            }\n          } else if (node === this.#root &&\n                     this.#root.nodeType === ELEMENT_NODE) {\n            matched.add(node);\n          }\n          break;\n        }\n        case 'only-of-type': {\n          if (parentNode) {\n            const [node1] = this._collectNthOfType({\n              a: 0,\n              b: 1\n            }, node);\n            if (node1 === node) {\n              const [node2] = this._collectNthOfType({\n                a: 0,\n                b: 1,\n                reverse: true\n              }, node);\n              if (node2 === node) {\n                matched.add(node);\n              }\n            }\n          } else if (node === this.#root &&\n                     this.#root.nodeType === ELEMENT_NODE) {\n            matched.add(node);\n          }\n          break;\n        }\n        case 'host':\n        case 'host-context': {\n          // ignore\n          break;\n        }\n        // legacy pseudo-elements\n        case 'after':\n        case 'before':\n        case 'first-letter':\n        case 'first-line': {\n          if (this.#warn) {\n            const msg = `Unsupported pseudo-element ::${astName}`;\n            throw new DOMException(msg, NOT_SUPPORTED_ERR);\n          }\n          break;\n        }\n        case 'active':\n        case 'autofill':\n        case 'blank':\n        case 'buffering':\n        case 'current':\n        case 'defined':\n        case 'focus-visible':\n        case 'fullscreen':\n        case 'future':\n        case 'hover':\n        case 'modal':\n        case 'muted':\n        case 'past':\n        case 'paused':\n        case 'picture-in-picture':\n        case 'playing':\n        case 'seeking':\n        case 'stalled':\n        case 'user-invalid':\n        case 'user-valid':\n        case 'volume-locked':\n        case '-webkit-autofill': {\n          if (this.#warn) {\n            const msg = `Unsupported pseudo-class :${astName}`;\n            throw new DOMException(msg, NOT_SUPPORTED_ERR);\n          }\n          break;\n        }\n        default: {\n          if (astName.startsWith('-webkit-')) {\n            if (this.#warn) {\n              const msg = `Unsupported pseudo-class :${astName}`;\n              throw new DOMException(msg, NOT_SUPPORTED_ERR);\n            }\n          } else if (!forgive) {\n            const msg = `Unknown pseudo-class :${astName}`;\n            throw new DOMException(msg, SYNTAX_ERR);\n          }\n        }\n      }\n    }\n    return matched;\n  }\n\n  /**\n   * match attribute selector\n   * @param {object} ast - AST\n   * @param {object} node - Element node\n   * @returns {?object} - matched node\n   */\n  _matchAttributeSelector(ast, node) {\n    const {\n      flags: astFlags, matcher: astMatcher, name: astName, value: astValue\n    } = ast;\n    if (typeof astFlags === 'string' && !/^[is]$/i.test(astFlags)) {\n      const css = generateCSS(ast);\n      const msg = `Invalid selector ${css}`;\n      throw new DOMException(msg, SYNTAX_ERR);\n    }\n    const { attributes } = node;\n    let res;\n    if (attributes && attributes.length) {\n      let caseInsensitive;\n      if (this.#document.contentType === 'text/html') {\n        if (typeof astFlags === 'string' && /^s$/i.test(astFlags)) {\n          caseInsensitive = false;\n        } else {\n          caseInsensitive = true;\n        }\n      } else if (typeof astFlags === 'string' && /^i$/i.test(astFlags)) {\n        caseInsensitive = true;\n      } else {\n        caseInsensitive = false;\n      }\n      let astAttrName = unescapeSelector(astName.name);\n      if (caseInsensitive) {\n        astAttrName = astAttrName.toLowerCase();\n      }\n      const attrValues = new Set();\n      // namespaced\n      if (astAttrName.indexOf('|') > -1) {\n        const {\n          prefix: astAttrPrefix, tagName: astAttrLocalName\n        } = selectorToNodeProps(astAttrName);\n        for (let { name: itemName, value: itemValue } of attributes) {\n          if (caseInsensitive) {\n            itemName = itemName.toLowerCase();\n            itemValue = itemValue.toLowerCase();\n          }\n          switch (astAttrPrefix) {\n            case '': {\n              if (astAttrLocalName === itemName) {\n                attrValues.add(itemValue);\n              }\n              break;\n            }\n            case '*': {\n              if (itemName.indexOf(':') > -1) {\n                if (itemName.endsWith(`:${astAttrLocalName}`)) {\n                  attrValues.add(itemValue);\n                }\n              } else if (astAttrLocalName === itemName) {\n                attrValues.add(itemValue);\n              }\n              break;\n            }\n            default: {\n              if (itemName.indexOf(':') > -1) {\n                const [itemNamePrefix, itemNameLocalName] = itemName.split(':');\n                if (astAttrPrefix === itemNamePrefix &&\n                    astAttrLocalName === itemNameLocalName &&\n                    isNamespaceDeclared(astAttrPrefix, node)) {\n                  attrValues.add(itemValue);\n                }\n              }\n            }\n          }\n        }\n      } else {\n        for (let { name: itemName, value: itemValue } of attributes) {\n          if (caseInsensitive) {\n            itemName = itemName.toLowerCase();\n            itemValue = itemValue.toLowerCase();\n          }\n          if (itemName.indexOf(':') > -1) {\n            const [itemNamePrefix, itemNameLocalName] = itemName.split(':');\n            // ignore xml:lang\n            if (itemNamePrefix === 'xml' && itemNameLocalName === 'lang') {\n              continue;\n            } else if (astAttrName === itemNameLocalName) {\n              attrValues.add(itemValue);\n            }\n          } else if (astAttrName === itemName) {\n            attrValues.add(itemValue);\n          }\n        }\n      }\n      if (attrValues.size) {\n        const {\n          name: astAttrIdentValue, value: astAttrStringValue\n        } = astValue || {};\n        let attrValue;\n        if (astAttrIdentValue) {\n          if (caseInsensitive) {\n            attrValue = astAttrIdentValue.toLowerCase();\n          } else {\n            attrValue = astAttrIdentValue;\n          }\n        } else if (astAttrStringValue) {\n          if (caseInsensitive) {\n            attrValue = astAttrStringValue.toLowerCase();\n          } else {\n            attrValue = astAttrStringValue;\n          }\n        } else if (astAttrStringValue === '') {\n          attrValue = astAttrStringValue;\n        }\n        switch (astMatcher) {\n          case '=': {\n            if (typeof attrValue === 'string' && attrValues.has(attrValue)) {\n              res = node;\n            }\n            break;\n          }\n          case '~=': {\n            if (attrValue && typeof attrValue === 'string') {\n              for (const value of attrValues) {\n                const item = new Set(value.split(/\\s+/));\n                if (item.has(attrValue)) {\n                  res = node;\n                  break;\n                }\n              }\n            }\n            break;\n          }\n          case '|=': {\n            if (attrValue && typeof attrValue === 'string') {\n              let item;\n              for (const value of attrValues) {\n                if (value === attrValue || value.startsWith(`${attrValue}-`)) {\n                  item = value;\n                  break;\n                }\n              }\n              if (item) {\n                res = node;\n              }\n            }\n            break;\n          }\n          case '^=': {\n            if (attrValue && typeof attrValue === 'string') {\n              let item;\n              for (const value of attrValues) {\n                if (value.startsWith(`${attrValue}`)) {\n                  item = value;\n                  break;\n                }\n              }\n              if (item) {\n                res = node;\n              }\n            }\n            break;\n          }\n          case '$=': {\n            if (attrValue && typeof attrValue === 'string') {\n              let item;\n              for (const value of attrValues) {\n                if (value.endsWith(`${attrValue}`)) {\n                  item = value;\n                  break;\n                }\n              }\n              if (item) {\n                res = node;\n              }\n            }\n            break;\n          }\n          case '*=': {\n            if (attrValue && typeof attrValue === 'string') {\n              let item;\n              for (const value of attrValues) {\n                if (value.includes(`${attrValue}`)) {\n                  item = value;\n                  break;\n                }\n              }\n              if (item) {\n                res = node;\n              }\n            }\n            break;\n          }\n          case null:\n          default: {\n            res = node;\n          }\n        }\n      }\n    }\n    return res ?? null;\n  }\n\n  /**\n   * match class selector\n   * @param {object} ast - AST\n   * @param {object} node - Element node\n   * @returns {?object} - matched node\n   */\n  _matchClassSelector(ast, node) {\n    const astName = unescapeSelector(ast.name);\n    let res;\n    if (node.classList.contains(astName)) {\n      res = node;\n    }\n    return res ?? null;\n  }\n\n  /**\n   * match ID selector\n   * @param {object} ast - AST\n   * @param {object} node - Element node\n   * @returns {?object} - matched node\n   */\n  _matchIDSelector(ast, node) {\n    const astName = unescapeSelector(ast.name);\n    const { id } = node;\n    let res;\n    if (astName === id) {\n      res = node;\n    }\n    return res ?? null;\n  }\n\n  /**\n   * match type selector\n   * @param {object} ast - AST\n   * @param {object} node - Element node\n   * @param {object} [opt] - options\n   * @param {boolean} [opt.forgive] - is forgiving selector list\n   * @returns {?object} - matched node\n   */\n  _matchTypeSelector(ast, node, opt = {}) {\n    const astName = unescapeSelector(ast.name);\n    const { localName, prefix } = node;\n    const { forgive } = opt;\n    let {\n      prefix: astPrefix, tagName: astNodeName\n    } = selectorToNodeProps(astName, node);\n    if (this.#document.contentType === 'text/html') {\n      astPrefix = astPrefix.toLowerCase();\n      astNodeName = astNodeName.toLowerCase();\n    }\n    let nodePrefix;\n    let nodeName;\n    // just in case that the namespaced content is parsed as text/html\n    if (localName.indexOf(':') > -1) {\n      [nodePrefix, nodeName] = localName.split(':');\n    } else {\n      nodePrefix = prefix || '';\n      nodeName = localName;\n    }\n    let res;\n    if (astPrefix === '' && nodePrefix === '') {\n      if (node.namespaceURI === null &&\n          (astNodeName === '*' || astNodeName === nodeName)) {\n        res = node;\n      }\n    } else if (astPrefix === '*') {\n      if (astNodeName === '*' || astNodeName === nodeName) {\n        res = node;\n      }\n    } else if (astPrefix === nodePrefix) {\n      if (isNamespaceDeclared(astPrefix, node)) {\n        if (astNodeName === '*' || astNodeName === nodeName) {\n          res = node;\n        }\n      } else if (!forgive) {\n        const msg = `Undeclared namespace ${astPrefix}`;\n        throw new DOMException(msg, SYNTAX_ERR);\n      }\n    } else if (astPrefix && !forgive && !isNamespaceDeclared(astPrefix, node)) {\n      const msg = `Undeclared namespace ${astPrefix}`;\n      throw new DOMException(msg, SYNTAX_ERR);\n    }\n    return res ?? null;\n  };\n\n  /**\n   * match shadow host pseudo class\n   * @param {object} ast - AST\n   * @param {object} node - DocumentFragment node\n   * @returns {?object} - matched node\n   */\n  _matchShadowHostPseudoClass(ast, node) {\n    const { children: astChildren } = ast;\n    const astName = unescapeSelector(ast.name);\n    let res;\n    if (Array.isArray(astChildren)) {\n      const [branch] = walkAST(astChildren[0]);\n      const [...leaves] = branch;\n      const { host } = node;\n      if (astName === 'host') {\n        let bool;\n        for (const leaf of leaves) {\n          const { type: leafType } = leaf;\n          if (leafType === COMBINATOR) {\n            const css = generateCSS(ast);\n            const msg = `Invalid selector ${css}`;\n            throw new DOMException(msg, SYNTAX_ERR);\n          }\n          bool = this._matchSelector(leaf, host).has(host);\n          if (!bool) {\n            break;\n          }\n        }\n        if (bool) {\n          res = node;\n        }\n      } else if (astName === 'host-context') {\n        let bool;\n        let parent = host;\n        while (parent) {\n          for (const leaf of leaves) {\n            const { type: leafType } = leaf;\n            if (leafType === COMBINATOR) {\n              const css = generateCSS(ast);\n              const msg = `Invalid selector ${css}`;\n              throw new DOMException(msg, SYNTAX_ERR);\n            }\n            bool = this._matchSelector(leaf, parent).has(parent);\n            if (!bool) {\n              break;\n            }\n          }\n          if (bool) {\n            break;\n          } else {\n            parent = parent.parentNode;\n          }\n        }\n        if (bool) {\n          res = node;\n        }\n      }\n    } else if (astName === 'host') {\n      res = node;\n    } else {\n      const msg = `Invalid selector :${astName}`;\n      throw new DOMException(msg, SYNTAX_ERR);\n    }\n    return res ?? null;\n  }\n\n  /**\n   * match selector\n   * @param {object} ast - AST\n   * @param {object} node - Document, DocumentFragment, Element node\n   * @param {object} [opt] - options\n   * @returns {Set.<object>} - collection of matched nodes\n   */\n  _matchSelector(ast, node, opt) {\n    const { type: astType } = ast;\n    const astName = unescapeSelector(ast.name);\n    let matched = new Set();\n    if (node.nodeType === ELEMENT_NODE) {\n      switch (astType) {\n        case SELECTOR_ATTR: {\n          const res = this._matchAttributeSelector(ast, node);\n          if (res) {\n            matched.add(res);\n          }\n          break;\n        }\n        case SELECTOR_CLASS: {\n          const res = this._matchClassSelector(ast, node);\n          if (res) {\n            matched.add(res);\n          }\n          break;\n        }\n        case SELECTOR_ID: {\n          const res = this._matchIDSelector(ast, node);\n          if (res) {\n            matched.add(res);\n          }\n          break;\n        }\n        case SELECTOR_PSEUDO_CLASS: {\n          const nodes = this._matchPseudoClassSelector(ast, node, opt);\n          if (nodes.size) {\n            matched = nodes;\n          }\n          break;\n        }\n        case SELECTOR_PSEUDO_ELEMENT: {\n          this._matchPseudoElementSelector(astName, opt);\n          break;\n        }\n        case SELECTOR_TYPE:\n        default: {\n          const res = this._matchTypeSelector(ast, node, opt);\n          if (res) {\n            matched.add(res);\n          }\n        }\n      }\n    } else if (this.#shadow && astType === SELECTOR_PSEUDO_CLASS &&\n               node.nodeType === DOCUMENT_FRAGMENT_NODE) {\n      if (astName !== 'has' && REG_LOGICAL_PSEUDO.test(astName)) {\n        const nodes = this._matchPseudoClassSelector(ast, node, opt);\n        if (nodes.size) {\n          matched = nodes;\n        }\n      } else if (REG_SHADOW_HOST.test(astName)) {\n        const res = this._matchShadowHostPseudoClass(ast, node);\n        if (res) {\n          matched.add(res);\n        }\n      }\n    }\n    return matched;\n  }\n\n  /**\n   * match leaves\n   * @param {Array.<object>} leaves - AST leaves\n   * @param {object} node - node\n   * @param {object} [opt] - options\n   * @returns {boolean} - result\n   */\n  _matchLeaves(leaves, node, opt) {\n    let bool;\n    for (const leaf of leaves) {\n      bool = this._matchSelector(leaf, node, opt).has(node);\n      if (!bool) {\n        break;\n      }\n    }\n    return !!bool;\n  }\n\n  /**\n   * find descendant nodes\n   * @param {Array.<object>} leaves - AST leaves\n   * @param {object} baseNode - base Element node\n   * @returns {object} - collection of nodes and pending state\n   */\n  _findDescendantNodes(leaves, baseNode) {\n    const [leaf, ...filterLeaves] = leaves;\n    const { type: leafType } = leaf;\n    const leafName = unescapeSelector(leaf.name);\n    const compound = filterLeaves.length > 0;\n    let nodes = new Set();\n    let pending = false;\n    if (this.#shadow) {\n      pending = true;\n    } else {\n      switch (leafType) {\n        case SELECTOR_ID: {\n          if (this.#root.nodeType === ELEMENT_NODE) {\n            pending = true;\n          } else {\n            const node = this.#root.getElementById(leafName);\n            if (node && node !== baseNode && baseNode.contains(node)) {\n              if (compound) {\n                const bool = this._matchLeaves(filterLeaves, node);\n                if (bool) {\n                  nodes.add(node);\n                }\n              } else {\n                nodes.add(node);\n              }\n            }\n          }\n          break;\n        }\n        case SELECTOR_CLASS: {\n          const arr = [].slice.call(baseNode.getElementsByClassName(leafName));\n          if (arr.length) {\n            if (compound) {\n              for (const node of arr) {\n                const bool = this._matchLeaves(filterLeaves, node);\n                if (bool) {\n                  nodes.add(node);\n                }\n              }\n            } else {\n              nodes = new Set(arr);\n            }\n          }\n          break;\n        }\n        case SELECTOR_TYPE: {\n          if (this.#document.contentType === 'text/html' &&\n              !/[*|]/.test(leafName)) {\n            const arr = [].slice.call(baseNode.getElementsByTagName(leafName));\n            if (arr.length) {\n              if (compound) {\n                for (const node of arr) {\n                  const bool = this._matchLeaves(filterLeaves, node);\n                  if (bool) {\n                    nodes.add(node);\n                  }\n                }\n              } else {\n                nodes = new Set(arr);\n              }\n            }\n          } else {\n            pending = true;\n          }\n          break;\n        }\n        case SELECTOR_PSEUDO_ELEMENT: {\n          this._matchPseudoElementSelector(leafName);\n          break;\n        }\n        default: {\n          pending = true;\n        }\n      }\n    }\n    return {\n      nodes,\n      pending\n    };\n  }\n\n  /**\n   * match combinator\n   * @param {object} twig - twig\n   * @param {object} node - Element node\n   * @param {object} [opt] - option\n   * @param {string} [opt.dir] - direction to find\n   * @param {boolean} [opt.forgive] - is forgiving selector list\n   * @returns {Set.<object>} - collection of matched nodes\n   */\n  _matchCombinator(twig, node, opt = {}) {\n    const { combo, leaves } = twig;\n    const { name: comboName } = combo;\n    const { dir, forgive } = opt;\n    let matched = new Set();\n    if (dir === DIR_NEXT) {\n      switch (comboName) {\n        case '+': {\n          const refNode = node.nextElementSibling;\n          if (refNode) {\n            const bool = this._matchLeaves(leaves, refNode, { forgive });\n            if (bool) {\n              matched.add(refNode);\n            }\n          }\n          break;\n        }\n        case '~': {\n          const { parentNode } = node;\n          if (parentNode) {\n            const walker =\n              this.#document.createTreeWalker(parentNode, SHOW_ELEMENT);\n            let refNode = this._traverse(node, walker);\n            if (refNode === node) {\n              refNode = walker.nextSibling();\n            }\n            while (refNode) {\n              const bool = this._matchLeaves(leaves, refNode, { forgive });\n              if (bool) {\n                matched.add(refNode);\n              }\n              refNode = walker.nextSibling();\n            }\n          }\n          break;\n        }\n        case '>': {\n          const walker = this.#document.createTreeWalker(node, SHOW_ELEMENT);\n          let refNode = walker.firstChild();\n          while (refNode) {\n            const bool = this._matchLeaves(leaves, refNode, { forgive });\n            if (bool) {\n              matched.add(refNode);\n            }\n            refNode = walker.nextSibling();\n          }\n          break;\n        }\n        case ' ':\n        default: {\n          const { nodes, pending } = this._findDescendantNodes(leaves, node);\n          if (nodes.size) {\n            matched = nodes;\n          } else if (pending) {\n            const walker = this.#document.createTreeWalker(node, SHOW_ELEMENT);\n            let refNode = walker.nextNode();\n            while (refNode) {\n              const bool = this._matchLeaves(leaves, refNode, { forgive });\n              if (bool) {\n                matched.add(refNode);\n              }\n              refNode = walker.nextNode();\n            }\n          }\n        }\n      }\n    } else {\n      switch (comboName) {\n        case '+': {\n          const refNode = node.previousElementSibling;\n          if (refNode) {\n            const bool = this._matchLeaves(leaves, refNode, { forgive });\n            if (bool) {\n              matched.add(refNode);\n            }\n          }\n          break;\n        }\n        case '~': {\n          const walker =\n            this.#document.createTreeWalker(node.parentNode, SHOW_ELEMENT);\n          let refNode = walker.firstChild();\n          while (refNode) {\n            if (refNode === node) {\n              break;\n            } else {\n              const bool = this._matchLeaves(leaves, refNode, { forgive });\n              if (bool) {\n                matched.add(refNode);\n              }\n            }\n            refNode = walker.nextSibling();\n          }\n          break;\n        }\n        case '>': {\n          const refNode = node.parentNode;\n          if (refNode) {\n            const bool = this._matchLeaves(leaves, refNode, { forgive });\n            if (bool) {\n              matched.add(refNode);\n            }\n          }\n          break;\n        }\n        case ' ':\n        default: {\n          const arr = [];\n          let refNode = node.parentNode;\n          while (refNode) {\n            const bool = this._matchLeaves(leaves, refNode, { forgive });\n            if (bool) {\n              arr.push(refNode);\n            }\n            refNode = refNode.parentNode;\n          }\n          if (arr.length) {\n            matched = new Set(arr.reverse());\n          }\n        }\n      }\n    }\n    return matched;\n  }\n\n  /**\n   * find matched node from tree walker\n   * @param {Array.<object>} leaves - AST leaves\n   * @param {object} [opt] - options\n   * @param {object} [opt.node] - node to start from\n   * @param {object} [opt.tree] - tree walker\n   * @returns {?object} - matched node\n   */\n  _findNode(leaves, opt = {}) {\n    let { node, walker } = opt;\n    if (!walker) {\n      walker = this.#tree;\n    }\n    let matchedNode;\n    let refNode = this._traverse(node, walker);\n    if (refNode) {\n      if (refNode.nodeType !== ELEMENT_NODE) {\n        refNode = walker.nextNode();\n      } else if (refNode === node) {\n        if (refNode !== this.#root) {\n          refNode = walker.nextNode();\n        }\n      }\n      while (refNode) {\n        let bool;\n        if (this.#node.nodeType === ELEMENT_NODE) {\n          if (refNode === this.#node) {\n            bool = true;\n          } else {\n            bool = this.#node.contains(refNode);\n          }\n        } else {\n          bool = true;\n        }\n        if (bool) {\n          const matched = this._matchLeaves(leaves, refNode);\n          if (matched) {\n            matchedNode = refNode;\n            break;\n          }\n        }\n        refNode = walker.nextNode();\n      }\n    }\n    return matchedNode ?? null;\n  }\n\n  /**\n   * find entry nodes\n   * @param {object} twig - twig\n   * @param {string} targetType - target type\n   * @returns {object} - collection of nodes etc.\n   */\n  _findEntryNodes(twig, targetType) {\n    const { leaves } = twig;\n    const [leaf, ...filterLeaves] = leaves;\n    const { type: leafType } = leaf;\n    const leafName = unescapeSelector(leaf.name);\n    const compound = filterLeaves.length > 0;\n    let nodes = new Set();\n    let filtered = false;\n    let pending = false;\n    switch (leafType) {\n      case SELECTOR_ID: {\n        if (targetType === TARGET_SELF) {\n          const bool = this._matchLeaves(leaves, this.#node);\n          if (bool) {\n            nodes.add(this.#node);\n            filtered = true;\n          }\n        } else if (targetType === TARGET_LINEAL) {\n          let refNode = this.#node;\n          while (refNode) {\n            const bool = this._matchLeaves(leaves, refNode);\n            if (bool) {\n              nodes.add(refNode);\n              filtered = true;\n            }\n            refNode = refNode.parentNode;\n          }\n        } else if (targetType === TARGET_ALL ||\n                   this.#root.nodeType === ELEMENT_NODE) {\n          pending = true;\n        } else {\n          const node = this.#root.getElementById(leafName);\n          if (node) {\n            nodes.add(node);\n            filtered = true;\n          }\n        }\n        break;\n      }\n      case SELECTOR_CLASS: {\n        if (targetType === TARGET_SELF) {\n          if (this.#node.nodeType === ELEMENT_NODE &&\n              this.#node.classList.contains(leafName)) {\n            nodes.add(this.#node);\n          }\n        } else if (targetType === TARGET_LINEAL) {\n          let refNode = this.#node;\n          while (refNode) {\n            if (refNode.nodeType === ELEMENT_NODE) {\n              if (refNode.classList.contains(leafName)) {\n                nodes.add(refNode);\n              }\n              refNode = refNode.parentNode;\n            } else {\n              break;\n            }\n          }\n        } else if (targetType === TARGET_FIRST) {\n          const node = this._findNode(leaves, {\n            node: this.#node,\n            walker: this.#finder\n          });\n          if (node) {\n            nodes.add(node);\n            filtered = true;\n            break;\n          }\n        } else if (this.#root.nodeType === DOCUMENT_FRAGMENT_NODE ||\n                   this.#root.nodeType === ELEMENT_NODE) {\n          pending = true;\n        } else {\n          const arr =\n            [].slice.call(this.#root.getElementsByClassName(leafName));\n          if (this.#node.nodeType === ELEMENT_NODE) {\n            for (const node of arr) {\n              if (node === this.#node || isInclusive(node, this.#node)) {\n                nodes.add(node);\n              }\n            }\n          } else if (arr.length) {\n            nodes = new Set(arr);\n          }\n        }\n        break;\n      }\n      case SELECTOR_TYPE: {\n        if (targetType === TARGET_SELF) {\n          if (this.#node.nodeType === ELEMENT_NODE) {\n            const bool = this._matchLeaves(leaves, this.#node);\n            if (bool) {\n              nodes.add(this.#node);\n              filtered = true;\n            }\n          }\n        } else if (targetType === TARGET_LINEAL) {\n          let refNode = this.#node;\n          while (refNode) {\n            if (refNode.nodeType === ELEMENT_NODE) {\n              const bool = this._matchLeaves(leaves, refNode);\n              if (bool) {\n                nodes.add(refNode);\n                filtered = true;\n              }\n              refNode = refNode.parentNode;\n            } else {\n              break;\n            }\n          }\n        } else if (targetType === TARGET_FIRST) {\n          const node = this._findNode(leaves, {\n            node: this.#node,\n            walker: this.#finder\n          });\n          if (node) {\n            nodes.add(node);\n            filtered = true;\n            break;\n          }\n        } else if (this.#document.contentType !== 'text/html' ||\n                   /[*|]/.test(leafName) ||\n                   this.#root.nodeType === DOCUMENT_FRAGMENT_NODE ||\n                   this.#root.nodeType === ELEMENT_NODE) {\n          pending = true;\n        } else {\n          const arr = [].slice.call(this.#root.getElementsByTagName(leafName));\n          if (this.#node.nodeType === ELEMENT_NODE) {\n            for (const node of arr) {\n              if (node === this.#node || isInclusive(node, this.#node)) {\n                nodes.add(node);\n              }\n            }\n          } else if (arr.length) {\n            nodes = new Set(arr);\n          }\n        }\n        break;\n      }\n      case SELECTOR_PSEUDO_ELEMENT: {\n        // throws\n        this._matchPseudoElementSelector(leafName);\n        break;\n      }\n      default: {\n        if (targetType !== TARGET_LINEAL && REG_SHADOW_HOST.test(leafName)) {\n          if (this.#shadow && this.#node.nodeType === DOCUMENT_FRAGMENT_NODE) {\n            const node = this._matchShadowHostPseudoClass(leaf, this.#node);\n            if (node) {\n              nodes.add(node);\n            }\n          }\n        } else if (targetType === TARGET_SELF) {\n          const bool = this._matchLeaves(leaves, this.#node);\n          if (bool) {\n            nodes.add(this.#node);\n            filtered = true;\n          }\n        } else if (targetType === TARGET_LINEAL) {\n          let refNode = this.#node;\n          while (refNode) {\n            const bool = this._matchLeaves(leaves, refNode);\n            if (bool) {\n              nodes.add(refNode);\n              filtered = true;\n            }\n            refNode = refNode.parentNode;\n          }\n        } else if (targetType === TARGET_FIRST) {\n          const node = this._findNode(leaves, {\n            node: this.#node,\n            walker: this.#finder\n          });\n          if (node) {\n            nodes.add(node);\n            filtered = true;\n            break;\n          }\n        } else {\n          pending = true;\n        }\n      }\n    }\n    return {\n      compound,\n      filtered,\n      nodes,\n      pending\n    };\n  }\n\n  /**\n   * get entry twig\n   * @param {Array.<object>} branch - AST branch\n   * @param {string} targetType - target type\n   * @returns {object} - direction and twig\n   */\n  _getEntryTwig(branch, targetType) {\n    const branchLen = branch.length;\n    const complex = branchLen > 1;\n    const firstTwig = branch[0];\n    let dir;\n    let twig;\n    if (complex) {\n      const { combo: firstCombo, leaves: [{ type: firstType }] } = firstTwig;\n      const lastTwig = branch[branchLen - 1];\n      const { leaves: [{ type: lastType }] } = lastTwig;\n      if (lastType === SELECTOR_PSEUDO_ELEMENT || lastType === SELECTOR_ID) {\n        dir = DIR_PREV;\n        twig = lastTwig;\n      } else if (firstType === SELECTOR_PSEUDO_ELEMENT ||\n                 firstType === SELECTOR_ID) {\n        dir = DIR_NEXT;\n        twig = firstTwig;\n      } else if (targetType === TARGET_ALL) {\n        if (branchLen === 2) {\n          const { name: comboName } = firstCombo;\n          if (/^[+~]$/.test(comboName)) {\n            dir = DIR_PREV;\n            twig = lastTwig;\n          } else {\n            dir = DIR_NEXT;\n            twig = firstTwig;\n          }\n        } else {\n          dir = DIR_NEXT;\n          twig = firstTwig;\n        }\n      } else {\n        let bool;\n        let sibling;\n        for (const { combo, leaves: [leaf] } of branch) {\n          const { type: leafType } = leaf;\n          const leafName = unescapeSelector(leaf.name);\n          if (leafType === SELECTOR_PSEUDO_CLASS && leafName === 'dir') {\n            bool = false;\n            break;\n          }\n          if (combo && !sibling) {\n            const { name: comboName } = combo;\n            if (/^[+~]$/.test(comboName)) {\n              bool = true;\n              sibling = true;\n            }\n          }\n        }\n        if (bool) {\n          dir = DIR_NEXT;\n          twig = firstTwig;\n        } else {\n          dir = DIR_PREV;\n          twig = lastTwig;\n        }\n      }\n    } else {\n      dir = DIR_PREV;\n      twig = firstTwig;\n    }\n    return {\n      complex,\n      dir,\n      twig\n    };\n  }\n\n  /**\n   * collect nodes\n   * @param {string} targetType - target type\n   * @returns {Array.<Array.<object|undefined>>} - #ast and #nodes\n   */\n  _collectNodes(targetType) {\n    const ast = this.#ast.values();\n    if (targetType === TARGET_ALL || targetType === TARGET_FIRST) {\n      const pendingItems = new Set();\n      let i = 0;\n      for (const { branch } of ast) {\n        const { dir, twig } = this._getEntryTwig(branch, targetType);\n        const {\n          compound, filtered, nodes, pending\n        } = this._findEntryNodes(twig, targetType);\n        if (nodes.size) {\n          this.#ast[i].find = true;\n          this.#nodes[i] = nodes;\n        } else if (pending) {\n          pendingItems.add(new Map([\n            ['index', i],\n            ['twig', twig]\n          ]));\n        }\n        this.#ast[i].dir = dir;\n        this.#ast[i].filtered = filtered || !compound;\n        i++;\n      }\n      if (pendingItems.size) {\n        let node;\n        let walker;\n        if (this.#node !== this.#root && this.#node.nodeType === ELEMENT_NODE) {\n          node = this.#node;\n          walker = this.#finder;\n        } else {\n          node = this.#root;\n          walker = this.#tree;\n        }\n        let nextNode = this._traverse(node, walker);\n        while (nextNode) {\n          let bool = false;\n          if (this.#node.nodeType === ELEMENT_NODE) {\n            if (nextNode === this.#node) {\n              bool = true;\n            } else {\n              bool = this.#node.contains(nextNode);\n            }\n          } else {\n            bool = true;\n          }\n          if (bool) {\n            for (const pendingItem of pendingItems) {\n              const { leaves } = pendingItem.get('twig');\n              const matched = this._matchLeaves(leaves, nextNode);\n              if (matched) {\n                const index = pendingItem.get('index');\n                this.#ast[index].filtered = true;\n                this.#ast[index].find = true;\n                this.#nodes[index].add(nextNode);\n              }\n            }\n          }\n          nextNode = walker.nextNode();\n        }\n      }\n    } else {\n      let i = 0;\n      for (const { branch } of ast) {\n        const twig = branch[branch.length - 1];\n        const {\n          compound, filtered, nodes\n        } = this._findEntryNodes(twig, targetType);\n        if (nodes.size) {\n          this.#ast[i].find = true;\n          this.#nodes[i] = nodes;\n        }\n        this.#ast[i].dir = DIR_PREV;\n        this.#ast[i].filtered = filtered || !compound;\n        i++;\n      }\n    }\n    return [\n      this.#ast,\n      this.#nodes\n    ];\n  }\n\n  /**\n   * sort nodes\n   * @param {Array.<object>|Set.<object>} nodes - collection of nodes\n   * @returns {Array.<object|undefined>} - collection of sorted nodes\n   */\n  _sortNodes(nodes) {\n    const arr = [...nodes];\n    if (arr.length > 1) {\n      arr.sort((a, b) => {\n        let res;\n        if (isPreceding(b, a)) {\n          res = 1;\n        } else {\n          res = -1;\n        }\n        return res;\n      });\n    }\n    return arr;\n  }\n\n  /**\n   * match nodes\n   * @param {string} targetType - target type\n   * @returns {Set.<object>} - collection of matched nodes\n   */\n  _matchNodes(targetType) {\n    const [...branches] = this.#ast;\n    const l = branches.length;\n    let nodes = new Set();\n    for (let i = 0; i < l; i++) {\n      const { branch, dir, filtered, find } = branches[i];\n      const branchLen = branch.length;\n      if (branchLen && find) {\n        const entryNodes = this.#nodes[i];\n        const lastIndex = branchLen - 1;\n        if (lastIndex === 0) {\n          const { leaves: [, ...filterLeaves] } = branch[0];\n          if ((targetType === TARGET_ALL || targetType === TARGET_FIRST) &&\n              this.#node.nodeType === ELEMENT_NODE) {\n            for (const node of entryNodes) {\n              const bool = filtered || this._matchLeaves(filterLeaves, node);\n              if (bool && node !== this.#node && this.#node.contains(node)) {\n                nodes.add(node);\n                if (targetType !== TARGET_ALL) {\n                  break;\n                }\n              }\n            }\n          } else if (!filterLeaves.length) {\n            if (targetType === TARGET_ALL) {\n              const n = [...nodes];\n              nodes = new Set([...n, ...entryNodes]);\n            } else {\n              const [node] = [...entryNodes];\n              nodes.add(node);\n            }\n          } else {\n            for (const node of entryNodes) {\n              const bool = filtered || this._matchLeaves(filterLeaves, node);\n              if (bool) {\n                nodes.add(node);\n                if (targetType !== TARGET_ALL) {\n                  break;\n                }\n              }\n            }\n          }\n        } else if (dir === DIR_NEXT) {\n          let { combo, leaves: entryLeaves } = branch[0];\n          const [, ...filterLeaves] = entryLeaves;\n          let matched;\n          for (const node of entryNodes) {\n            const bool = filtered || this._matchLeaves(filterLeaves, node);\n            if (bool) {\n              let nextNodes = new Set([node]);\n              for (let j = 1; j < branchLen; j++) {\n                const { combo: nextCombo, leaves } = branch[j];\n                const arr = [];\n                for (const nextNode of nextNodes) {\n                  const twig = {\n                    combo,\n                    leaves\n                  };\n                  const m = this._matchCombinator(twig, nextNode, { dir });\n                  if (m.size) {\n                    arr.push(...m);\n                  }\n                }\n                if (arr.length) {\n                  if (j === lastIndex) {\n                    if (targetType === TARGET_ALL) {\n                      const n = [...nodes];\n                      nodes = new Set([...n, ...arr]);\n                    } else {\n                      const [node] = this._sortNodes(arr);\n                      nodes.add(node);\n                    }\n                    matched = true;\n                  } else {\n                    combo = nextCombo;\n                    nextNodes = new Set(arr);\n                    matched = false;\n                  }\n                } else {\n                  matched = false;\n                  break;\n                }\n              }\n            } else {\n              matched = false;\n            }\n            if (matched && targetType !== TARGET_ALL) {\n              break;\n            }\n          }\n          if (!matched && targetType === TARGET_FIRST) {\n            const [entryNode] = [...entryNodes];\n            let refNode = this._findNode(entryLeaves, {\n              node: entryNode,\n              walker: this.#finder\n            });\n            while (refNode) {\n              let nextNodes = new Set([refNode]);\n              for (let j = 1; j < branchLen; j++) {\n                const { combo: nextCombo, leaves } = branch[j];\n                const arr = [];\n                for (const nextNode of nextNodes) {\n                  const twig = {\n                    combo,\n                    leaves\n                  };\n                  const m = this._matchCombinator(twig, nextNode, { dir });\n                  if (m.size) {\n                    arr.push(...m);\n                  }\n                }\n                if (arr.length) {\n                  if (j === lastIndex) {\n                    const [node] = this._sortNodes(arr);\n                    nodes.add(node);\n                    matched = true;\n                  } else {\n                    combo = nextCombo;\n                    nextNodes = new Set(arr);\n                    matched = false;\n                  }\n                } else {\n                  matched = false;\n                  break;\n                }\n              }\n              if (matched) {\n                break;\n              }\n              refNode = this._findNode(entryLeaves, {\n                node: refNode,\n                walker: this.#finder\n              });\n              nextNodes = new Set([refNode]);\n            }\n          }\n        } else {\n          const { leaves: entryLeaves } = branch[lastIndex];\n          const [, ...filterLeaves] = entryLeaves;\n          let matched;\n          for (const node of entryNodes) {\n            const bool = filtered || this._matchLeaves(filterLeaves, node);\n            if (bool) {\n              let nextNodes = new Set([node]);\n              for (let j = lastIndex - 1; j >= 0; j--) {\n                const twig = branch[j];\n                const arr = [];\n                for (const nextNode of nextNodes) {\n                  const m = this._matchCombinator(twig, nextNode, { dir });\n                  if (m.size) {\n                    arr.push(...m);\n                  }\n                }\n                if (arr.length) {\n                  if (j === 0) {\n                    nodes.add(node);\n                    matched = true;\n                  } else {\n                    nextNodes = new Set(arr);\n                    matched = false;\n                  }\n                } else {\n                  matched = false;\n                  break;\n                }\n              }\n            }\n            if (matched && targetType !== TARGET_ALL) {\n              break;\n            }\n          }\n          if (!matched && targetType === TARGET_FIRST) {\n            const [entryNode] = [...entryNodes];\n            let refNode = this._findNode(entryLeaves, {\n              node: entryNode,\n              walker: this.#finder\n            });\n            while (refNode) {\n              let nextNodes = new Set([refNode]);\n              for (let j = lastIndex - 1; j >= 0; j--) {\n                const twig = branch[j];\n                const arr = [];\n                for (const nextNode of nextNodes) {\n                  const m = this._matchCombinator(twig, nextNode, { dir });\n                  if (m.size) {\n                    arr.push(...m);\n                  }\n                }\n                if (arr.length) {\n                  if (j === 0) {\n                    nodes.add(refNode);\n                    matched = true;\n                  } else {\n                    nextNodes = new Set(arr);\n                    matched = false;\n                  }\n                } else {\n                  matched = false;\n                  break;\n                }\n              }\n              if (matched) {\n                break;\n              }\n              refNode = this._findNode(entryLeaves, {\n                node: refNode,\n                walker: this.#finder\n              });\n              nextNodes = new Set([refNode]);\n            }\n          }\n        }\n      }\n    }\n    return nodes;\n  }\n\n  /**\n   * find matched nodes\n   * @param {string} targetType - target type\n   * @returns {Set.<object>} - collection of matched nodes\n   */\n  _find(targetType) {\n    if (targetType === TARGET_ALL || targetType === TARGET_FIRST) {\n      this.#finder = this.#document.createTreeWalker(this.#node, SHOW_ELEMENT);\n    }\n    this._collectNodes(targetType);\n    const nodes = this._matchNodes(targetType);\n    return nodes;\n  }\n\n  /**\n   * matches\n   * @returns {boolean} - `true` if matched `false` otherwise\n   */\n  matches() {\n    if (this.#node.nodeType !== ELEMENT_NODE) {\n      const msg = `Unexpected node ${this.#node.nodeName}`;\n      this._onError(new TypeError(msg));\n    }\n    let res;\n    try {\n      const nodes = this._find(TARGET_SELF);\n      if (nodes.size) {\n        res = nodes.has(this.#node);\n      }\n    } catch (e) {\n      this._onError(e);\n    }\n    return !!res;\n  }\n\n  /**\n   * closest\n   * @returns {?object} - matched node\n   */\n  closest() {\n    if (this.#node.nodeType !== ELEMENT_NODE) {\n      const msg = `Unexpected node ${this.#node.nodeName}`;\n      this._onError(new TypeError(msg));\n    }\n    let res;\n    try {\n      const nodes = this._find(TARGET_LINEAL);\n      let node = this.#node;\n      while (node) {\n        if (nodes.has(node)) {\n          res = node;\n          break;\n        }\n        node = node.parentNode;\n      }\n    } catch (e) {\n      this._onError(e);\n    }\n    return res ?? null;\n  }\n\n  /**\n   * query selector\n   * @returns {?object} - matched node\n   */\n  querySelector() {\n    let res;\n    try {\n      const nodes = this._find(TARGET_FIRST);\n      nodes.delete(this.#node);\n      if (nodes.size) {\n        [res] = this._sortNodes(nodes);\n      }\n    } catch (e) {\n      this._onError(e);\n    }\n    return res ?? null;\n  }\n\n  /**\n   * query selector all\n   * NOTE: returns Array, not NodeList\n   * @returns {Array.<object|undefined>} - collection of matched nodes\n   */\n  querySelectorAll() {\n    let res;\n    try {\n      const nodes = this._find(TARGET_ALL);\n      nodes.delete(this.#node);\n      if (nodes.size) {\n        res = this._sortNodes(nodes);\n      }\n    } catch (e) {\n      this._onError(e);\n    }\n    return res ?? [];\n  }\n};\n"], "mappings": "6iBAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,aAAAE,IAAA,eAAAC,EAAAH,GAKA,IAAAI,EAAgC,iDAChCC,EAGO,yBACPC,EAEO,uBAGPC,EAOO,yBACP,MAAMC,EAAW,OACXC,EAAW,OACXC,EAAa,MACbC,EAAe,QACfC,EAAgB,SAChBC,EAAc,OAgCb,MAAMX,CAAQ,CAEnBY,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GASA,YAAYC,EAAUC,EAAMC,EAAM,CAAC,EAAG,CACpC,KAAM,CAAE,KAAAC,CAAK,EAAID,EACjB,KAAKb,GAAO,IAAI,IAAI,CAClB,CAAC,0BAAyB,QAAM,EAChC,CAAC,cAAa,QAAM,EACpB,CAAC,iBAAgB,QAAM,EACvB,CAAC,gBAAe,QAAM,EACtB,CAAC,gBAAe,QAAM,EACtB,CAAC,wBAAuB,QAAM,CAChC,CAAC,EACD,KAAKC,GAAS,IAAI,QAClB,KAAKG,GAAQQ,EACb,CAAC,KAAKF,GAAS,KAAKR,GAAW,KAAKI,GAAO,KAAKE,EAAK,EAAI,KAAK,OAAOI,CAAI,EACzE,KAAKL,MAAU,kBAAeK,CAAI,EAClC,CAAC,KAAKb,GAAM,KAAKM,EAAM,EAAI,KAAK,YAAYM,CAAQ,EACpD,KAAKF,GAAQ,CAAC,CAACK,CACjB,CAQA,SAASC,EAAG,CACV,IAAKA,aAAa,cACbA,aAAa,KAAKL,GAAQ,eAC3BK,EAAE,OAAS,oBACT,KAAKN,IACP,QAAQ,KAAKM,EAAE,OAAO,MAEnB,OAAIA,aAAa,aAChB,IAAI,KAAKL,GAAQ,aAAaK,EAAE,QAASA,EAAE,IAAI,EAC5CA,aAAa,UAChB,IAAI,KAAKL,GAAQ,UAAUK,EAAE,OAAO,EAEpCA,CAEV,CAOA,OAAOH,EAAM,CACX,IAAII,EACAC,EACJ,OAAQL,GAAM,SAAU,CACtB,KAAK,gBAAe,CAClBI,EAAWJ,EACXK,EAAOL,EACP,KACF,CACA,KAAK,yBAAwB,CAC3BI,EAAWJ,EAAK,cAChBK,EAAOL,EACP,KACF,CACA,KAAK,eAAc,CACjB,GAAIA,EAAK,cAAc,SAASA,CAAI,EAClCI,EAAWJ,EAAK,cAChBK,EAAOL,EAAK,kBACP,CACL,IAAIM,EAASN,EACb,KAAOM,GACDA,EAAO,YACTA,EAASA,EAAO,WAKpBF,EAAWE,EAAO,cAClBD,EAAOC,CACT,CACA,KACF,CACA,QAAS,CACP,IAAIC,EACJ,MAAIP,GAAM,SACRO,EAAM,mBAAmBP,EAAK,QAAQ,GAItCO,EAAM,mBADJ,OAAO,UAAU,SAAS,KAAKP,CAAI,EAAE,MAAM,YAAW,SAAO,CAC9B,GAE7B,IAAI,UAAUO,CAAG,CACzB,CACF,CACA,MAAMC,EAAS,gBAAgB,yBAAyB,eAClDC,EAASL,EAAS,iBAAiBC,EAAMG,CAAM,EAErD,MAAO,CADQJ,EAAS,YAGtBA,EACAC,EACAI,CACF,CACF,CAOA,YAAYC,EAAQ,CAClB,MAAMC,EAAM,CAAC,GAAGD,CAAM,EACtB,OAAIC,EAAI,OAAS,GACfA,EAAI,KAAK,CAACC,EAAGC,IAAM,CACjB,KAAM,CAAE,KAAMC,CAAM,EAAIF,EAClB,CAAE,KAAMG,CAAM,EAAIF,EAClBG,EAAO,KAAK5B,GAAK,IAAI0B,CAAK,EAC1BG,EAAO,KAAK7B,GAAK,IAAI2B,CAAK,EAChC,IAAIG,EACJ,OAAIF,IAASC,EACXC,EAAM,EACGF,EAAOC,EAChBC,EAAM,EAENA,EAAM,GAEDA,CACT,CAAC,EAEIP,CACT,CAOA,YAAYZ,EAAU,CACpB,IAAIoB,EACJ,GAAI,CACFA,KAAS,iBAAcpB,CAAQ,CACjC,OAASI,EAAG,CACV,KAAK,SAASA,CAAC,CACjB,CACA,MAAMiB,KAAW,WAAQD,CAAM,EACzBE,EAAM,CAAC,EACPC,EAAQ,CAAC,EACf,IAAIC,EAAI,EACR,SAAW,CAAC,GAAGC,CAAK,IAAKJ,EAAU,CACjC,MAAMK,EAAS,CAAC,EAChB,IAAIC,EAAOF,EAAM,MAAM,EACvB,GAAIE,GAAQA,EAAK,OAAS,aAAY,CACpC,MAAMhB,EAAS,IAAI,IACnB,KAAOgB,GAAM,CACX,GAAIA,EAAK,OAAS,aAAY,CAC5B,KAAM,CAACC,CAAQ,EAAIH,EACnB,GAAIG,EAAS,OAAS,aAAY,CAChC,MAAMpB,EAAM,sBAAsBmB,EAAK,IAAI,GAAGC,EAAS,IAAI,GAC3D,MAAM,IAAI,KAAK7B,GAAQ,aAAaS,EAAK,YAAU,CACrD,CACAkB,EAAO,KAAK,CACV,MAAOC,EACP,OAAQ,KAAK,YAAYhB,CAAM,CACjC,CAAC,EACDA,EAAO,MAAM,CACf,MAAWgB,GACThB,EAAO,IAAIgB,CAAI,EAEjB,GAAIF,EAAM,OACRE,EAAOF,EAAM,MAAM,MACd,CACLC,EAAO,KAAK,CACV,MAAO,KACP,OAAQ,KAAK,YAAYf,CAAM,CACjC,CAAC,EACDA,EAAO,MAAM,EACb,KACF,CACF,CACF,CACAW,EAAI,KAAK,CACP,OAAAI,EACA,IAAK,KACL,SAAU,GACV,KAAM,EACR,CAAC,EACDH,EAAMC,CAAC,EAAI,IAAI,IACfA,GACF,CACA,MAAO,CACLF,EACAC,CACF,CACF,CAQA,UAAUtB,EAAO,CAAC,EAAGS,EAAS,KAAKb,GAAO,CACxC,IAAIgC,EACAC,EAAUpB,EAAO,YACrB,GAAIT,EAAK,WAAa,gBAAgB6B,IAAY7B,EAChD4B,EAAUC,MACL,CACL,GAAIA,IAAYpB,EAAO,KACrB,KAAOoB,GACD,EAAAA,IAAYpB,EAAO,MAClBT,EAAK,WAAa,gBAAgB6B,IAAY7B,IAGnD6B,EAAUpB,EAAO,WAAW,EAGhC,GAAIT,EAAK,WAAa,eACpB,KAAO6B,GAAS,CACd,GAAIA,IAAY7B,EAAM,CACpB4B,EAAUC,EACV,KACF,CACAA,EAAUpB,EAAO,SAAS,CAC5B,MAEAmB,EAAUC,CAEd,CACA,OAAOD,GAAW,IACpB,CAYA,iBAAiBE,EAAK9B,EAAM,CAC1B,KAAM,CAAE,EAAAY,EAAG,EAAAC,EAAG,QAAAkB,EAAS,SAAAhC,CAAS,EAAI+B,EAC9B,CAAE,WAAAE,CAAW,EAAIhC,EACvB,IAAIiC,EAAU,IAAI,IACdC,EASJ,GARInC,IACE,KAAKV,GAAO,IAAIU,CAAQ,EAC1BmC,EAAmB,KAAK7C,GAAO,IAAIU,CAAQ,GAE3CmC,KAAmB,WAAQnC,CAAQ,EACnC,KAAKV,GAAO,IAAIU,EAAUmC,CAAgB,IAG1CF,EAAY,CACd,MAAMxB,EAAS,gBAAgB,yBAAyB,eAClDC,EAAS,KAAKnB,GAAU,iBAAiB0C,EAAYxB,CAAM,EACjE,IAAI2B,EAAI,EACJN,EAAUpB,EAAO,WAAW,EAChC,KAAOoB,GACLM,IACAN,EAAUpB,EAAO,YAAY,EAE/BoB,EAAU,KAAK,UAAUG,EAAYvB,CAAM,EAC3C,MAAM2B,EAAgB,IAAI,IAC1B,GAAIF,EAGF,IAFAL,EAAU,KAAK,UAAUG,EAAYvB,CAAM,EAC3CoB,EAAUpB,EAAO,WAAW,EACrBoB,GAAS,CACd,IAAIQ,EACJ,UAAW3B,KAAUwB,EAEnB,GADAG,EAAO,KAAK,aAAa3B,EAAQmB,CAAO,EACpC,CAACQ,EACH,MAGAA,GACFD,EAAc,IAAIP,CAAO,EAE3BA,EAAUpB,EAAO,YAAY,CAC/B,CAGF,GAAIG,IAAM,GACR,GAAIC,EAAI,GAAKA,GAAKsB,GAChB,GAAIC,EAAc,KAAM,CACtB,IAAIb,EAAI,EAOR,IANAM,EAAU,KAAK,UAAUG,EAAYvB,CAAM,EACvCsB,EACFF,EAAUpB,EAAO,UAAU,EAE3BoB,EAAUpB,EAAO,WAAW,EAEvBoB,GAAS,CACd,GAAIO,EAAc,IAAIP,CAAO,EAAG,CAC9B,GAAIN,IAAMV,EAAI,EAAG,CACfoB,EAAQ,IAAIJ,CAAO,EACnB,KACF,CACAN,GACF,CACIQ,EACFF,EAAUpB,EAAO,gBAAgB,EAEjCoB,EAAUpB,EAAO,YAAY,CAEjC,CACF,SAAW,CAACV,EAAU,CACpB,IAAIwB,EAAI,EAOR,IANAM,EAAU,KAAK,UAAUG,EAAYvB,CAAM,EACvCsB,EACFF,EAAUpB,EAAO,UAAU,EAE3BoB,EAAUpB,EAAO,WAAW,EAEvBoB,GAAS,CACd,GAAIN,IAAMV,EAAI,EAAG,CACfoB,EAAQ,IAAIJ,CAAO,EACnB,KACF,CACIE,EACFF,EAAUpB,EAAO,gBAAgB,EAEjCoB,EAAUpB,EAAO,YAAY,EAE/Bc,GACF,CACF,OAGG,CACL,IAAIe,EAAMzB,EAAI,EACd,GAAID,EAAI,EACN,KAAO0B,EAAM,GACXA,GAAO1B,EAGX,GAAI0B,GAAO,GAAKA,EAAMH,EAAG,CACvB,IAAIZ,EAAI,EACJgB,EAAI3B,EAAI,EAAI,EAAIC,EAAI,EAOxB,IANAgB,EAAU,KAAK,UAAUG,EAAYvB,CAAM,EACvCsB,EACFF,EAAUpB,EAAO,UAAU,EAE3BoB,EAAUpB,EAAO,WAAW,EAEvBoB,IACDA,GAAWS,GAAO,GAAKA,EAAMH,IAC3BC,EAAc,KACZA,EAAc,IAAIP,CAAO,IACvBU,IAAMD,IACRL,EAAQ,IAAIJ,CAAO,EACnBS,GAAO1B,GAELA,EAAI,EACN2B,IAEAA,KAGKhB,IAAMe,IACVvC,GACHkC,EAAQ,IAAIJ,CAAO,EAErBS,GAAO1B,GAELmB,EACFF,EAAUpB,EAAO,gBAAgB,EAEjCoB,EAAUpB,EAAO,YAAY,EAE/Bc,GAKN,CACF,CACA,GAAIQ,GAAWE,EAAQ,KAAO,EAAG,CAC/B,MAAMO,EAAI,CAAC,GAAGP,CAAO,EACrBA,EAAU,IAAI,IAAIO,EAAE,QAAQ,CAAC,CAC/B,CACF,SAAWxC,IAAS,KAAKN,IAAS,KAAKA,GAAM,WAAa,gBAC9CkB,EAAIC,IAAO,EACrB,GAAIqB,EAAkB,CACpB,IAAIG,EACJ,UAAW3B,KAAUwB,EAEnB,GADAG,EAAO,KAAK,aAAa3B,EAAQV,CAAI,EACjCqC,EACF,MAGAA,GACFJ,EAAQ,IAAIjC,CAAI,CAEpB,MACEiC,EAAQ,IAAIjC,CAAI,EAGpB,OAAOiC,CACT,CAWA,kBAAkBH,EAAK9B,EAAM,CAC3B,KAAM,CAAE,EAAAY,EAAG,EAAAC,EAAG,QAAAkB,CAAQ,EAAID,EACpB,CAAE,UAAAW,EAAW,WAAAT,EAAY,OAAAU,CAAO,EAAI1C,EAC1C,IAAIiC,EAAU,IAAI,IAClB,GAAID,EAAY,CACd,MAAMxB,EAAS,gBAAgB,yBAAyB,eAClDC,EAAS,KAAKnB,GAAU,iBAAiB0C,EAAYxB,CAAM,EACjE,IAAI2B,EAAI,EACJN,EAAUpB,EAAO,WAAW,EAChC,KAAOoB,GACLM,IACAN,EAAUpB,EAAO,YAAY,EAG/B,GAAIG,IAAM,GACR,GAAIC,EAAI,GAAKA,GAAKsB,EAAG,CACnB,IAAII,EAAI,EAOR,IANAV,EAAU,KAAK,UAAUG,EAAYvB,CAAM,EACvCsB,EACFF,EAAUpB,EAAO,UAAU,EAE3BoB,EAAUpB,EAAO,WAAW,EAEvBoB,GAAS,CACd,KAAM,CAAE,UAAWc,EAAe,OAAQC,CAAW,EAAIf,EACzD,GAAIc,IAAkBF,GAAaG,IAAeF,EAAQ,CACxD,GAAIH,IAAM1B,EAAI,EAAG,CACfoB,EAAQ,IAAIJ,CAAO,EACnB,KACF,CACAU,GACF,CACIR,EACFF,EAAUpB,EAAO,gBAAgB,EAEjCoB,EAAUpB,EAAO,YAAY,CAEjC,CACF,MAEK,CACL,IAAI6B,EAAMzB,EAAI,EACd,GAAID,EAAI,EACN,KAAO0B,EAAM,GACXA,GAAO1B,EAGX,GAAI0B,GAAO,GAAKA,EAAMH,EAAG,CACvB,IAAII,EAAI3B,EAAI,EAAI,EAAIC,EAAI,EAOxB,IANAgB,EAAU,KAAK,UAAUG,EAAYvB,CAAM,EACvCsB,EACFF,EAAUpB,EAAO,UAAU,EAE3BoB,EAAUpB,EAAO,WAAW,EAEvBoB,GAAS,CACd,KAAM,CAAE,UAAWc,EAAe,OAAQC,CAAW,EAAIf,EACzD,GAAIc,IAAkBF,GAAaG,IAAeF,EAAQ,CAKxD,GAJIH,IAAMD,IACRL,EAAQ,IAAIJ,CAAO,EACnBS,GAAO1B,GAEL0B,EAAM,GAAKA,GAAOH,EACpB,MACSvB,EAAI,EACb2B,IAEAA,GAEJ,CACIR,EACFF,EAAUpB,EAAO,gBAAgB,EAEjCoB,EAAUpB,EAAO,YAAY,CAEjC,CACF,CACF,CACA,GAAIsB,GAAWE,EAAQ,KAAO,EAAG,CAC/B,MAAM,EAAI,CAAC,GAAGA,CAAO,EACrBA,EAAU,IAAI,IAAI,EAAE,QAAQ,CAAC,CAC/B,CACF,MAAWjC,IAAS,KAAKN,IAAS,KAAKA,GAAM,WAAa,gBAC9CkB,EAAIC,IAAO,GACrBoB,EAAQ,IAAIjC,CAAI,EAElB,OAAOiC,CACT,CASA,cAAcZ,EAAKrB,EAAM6C,EAAS,CAChC,KAAM,CACJ,IAAK,CACH,EAAAjC,EACA,EAAAC,EACA,KAAMiC,CACR,EACA,SAAA/C,CACF,EAAIsB,EACE0B,KAAY,oBAAiBD,CAAY,EACzCE,EAAS,IAAI,IACfD,GACEA,IAAc,QAChBC,EAAO,IAAI,IAAK,CAAC,EACjBA,EAAO,IAAI,IAAK,CAAC,GACRD,IAAc,QACvBC,EAAO,IAAI,IAAK,CAAC,EACjBA,EAAO,IAAI,IAAK,CAAC,GAEfH,EAAQ,QAAQ,MAAM,EAAI,IAC5BG,EAAO,IAAI,UAAW,EAAI,IAGxB,OAAOpC,GAAM,UAAY,QAAQ,KAAKA,CAAC,EACzCoC,EAAO,IAAI,IAAKpC,EAAI,CAAC,EAErBoC,EAAO,IAAI,IAAK,CAAC,EAEf,OAAOnC,GAAM,UAAY,QAAQ,KAAKA,CAAC,EACzCmC,EAAO,IAAI,IAAKnC,EAAI,CAAC,EAErBmC,EAAO,IAAI,IAAK,CAAC,EAEfH,EAAQ,QAAQ,MAAM,EAAI,IAC5BG,EAAO,IAAI,UAAW,EAAI,GAG9B,IAAIf,EAAU,IAAI,IAClB,GAAIe,EAAO,IAAI,GAAG,GAAKA,EAAO,IAAI,GAAG,GACnC,GAAI,wBAAwB,KAAKH,CAAO,EAAG,CACrC9C,GACFiD,EAAO,IAAI,WAAYjD,CAAQ,EAEjC,MAAM+B,EAAM,OAAO,YAAYkB,CAAM,EAC/B1B,EAAQ,KAAK,iBAAiBQ,EAAK9B,CAAI,EACzCsB,EAAM,OACRW,EAAUX,EAEd,SAAW,0BAA0B,KAAKuB,CAAO,EAAG,CAClD,MAAMf,EAAM,OAAO,YAAYkB,CAAM,EAC/B1B,EAAQ,KAAK,kBAAkBQ,EAAK9B,CAAI,EAC1CsB,EAAM,OACRW,EAAUX,EAEd,EAEF,OAAOW,CACT,CAUA,4BAA4BgB,EAAShD,EAAM,CAAC,EAAG,CAC7C,KAAM,CAAE,QAAAiD,CAAQ,EAAIjD,EACpB,OAAQgD,EAAS,CACf,IAAK,QACL,IAAK,WACL,IAAK,SACL,IAAK,MACL,IAAK,aACL,IAAK,eACL,IAAK,aACL,IAAK,uBACL,IAAK,SACL,IAAK,cACL,IAAK,YACL,IAAK,cAAe,CAClB,GAAI,KAAKpD,GAAO,CACd,MAAMU,EAAM,gCAAgC0C,CAAO,GACnD,MAAM,IAAI,aAAa1C,EAAK,mBAAiB,CAC/C,CACA,KACF,CACA,IAAK,OACL,IAAK,UAAW,CACd,GAAI,KAAKV,GAAO,CACd,MAAMU,EAAM,gCAAgC0C,CAAO,KACnD,MAAM,IAAI,aAAa1C,EAAK,mBAAiB,CAC/C,CACA,KACF,CACA,QACE,GAAI0C,EAAQ,WAAW,UAAU,GAC/B,GAAI,KAAKpD,GAAO,CACd,MAAMU,EAAM,gCAAgC0C,CAAO,GACnD,MAAM,IAAI,aAAa1C,EAAK,mBAAiB,CAC/C,UACS,CAAC2C,EAAS,CACnB,MAAM3C,EAAM,4BAA4B0C,CAAO,GAC/C,MAAM,IAAI,aAAa1C,EAAK,YAAU,CACxC,CAEJ,CACF,CAQA,2BAA2Bc,EAAKrB,EAAM,CACpC,MAAMiD,KAAU,oBAAiB5B,EAAI,IAAI,EACnC8B,KAAM,qBAAkBnD,CAAI,EAClC,IAAIkB,EACJ,OAAI+B,IAAYE,IACdjC,EAAMlB,GAEDkB,GAAO,IAChB,CASA,0BAA0BG,EAAKrB,EAAM,CACnC,MAAMiD,KAAU,oBAAiB5B,EAAI,IAAI,EACzC,IAAIH,EACJ,GAAI+B,IAAY,IACd,GAAIjD,EAAK,aAAa,MAAM,EACtBA,EAAK,aAAa,MAAM,IAC1BkB,EAAMlB,OAEH,CACL,IAAIM,EAASN,EAAK,WAClB,KAAOM,GACDA,EAAO,WAAa,gBAAc,CACpC,GAAIA,EAAO,aAAa,MAAM,EAAG,CAC3BA,EAAO,aAAa,MAAM,IAC5BY,EAAMlB,GAER,KACF,CACAM,EAASA,EAAO,UAClB,CAIJ,SACS2C,EAAS,CAClB,MAAMG,EAAW,OAAO,WAAS,KAEjC,GADgB,IAAI,OAAO,aAAa,WAAS,GAAGA,CAAQ,IAAK,GAAG,EACxD,KAAKH,CAAO,EAAG,CACzB,IAAII,EACJ,GAAIJ,EAAQ,QAAQ,GAAG,EAAI,GAAI,CAC7B,KAAM,CAACK,EAAUC,EAAS,GAAGC,CAAQ,EAAIP,EAAQ,MAAM,GAAG,EAC1D,IAAIQ,EACAH,IAAa,IACfG,EAAe,GAAG,WAAS,GAAGL,CAAQ,GAEtCK,EAAe,GAAGH,CAAQ,GAAGF,CAAQ,GAEvC,MAAMM,EAAc,IAAIH,CAAO,GAAGH,CAAQ,GACpCO,EAAMH,EAAS,OACrB,IAAII,EAAe,GACnB,GAAID,EACF,QAASpC,EAAI,EAAGA,EAAIoC,EAAKpC,IACvBqC,GAAgB,IAAIJ,EAASjC,CAAC,CAAC,GAAG6B,CAAQ,GAG9CC,EACE,IAAI,OAAO,IAAII,CAAY,GAAGC,CAAW,GAAGE,CAAY,IAAK,GAAG,CACpE,MACEP,EAAkB,IAAI,OAAO,IAAIJ,CAAO,GAAGG,CAAQ,IAAK,GAAG,EAE7D,GAAIpD,EAAK,aAAa,MAAM,EACtBqD,EAAgB,KAAKrD,EAAK,aAAa,MAAM,CAAC,IAChDkB,EAAMlB,OAEH,CACL,IAAIM,EAASN,EAAK,WAClB,KAAOM,GACDA,EAAO,WAAa,gBAAc,CACpC,GAAIA,EAAO,aAAa,MAAM,EAAG,CAC/B,MAAMuD,EAAQvD,EAAO,aAAa,MAAM,EACpC+C,EAAgB,KAAKQ,CAAK,IAC5B3C,EAAMlB,GAER,KACF,CACAM,EAASA,EAAO,UAClB,CAIJ,CACF,CACF,CACA,OAAOY,GAAO,IAChB,CAQA,oBAAoBR,EAAQV,EAAM,CAChC,IAAIqC,EACJ,GAAI,MAAM,QAAQ3B,CAAM,GAAKA,EAAO,OAAQ,CAC1C,KAAM,CAACoD,CAAI,EAAIpD,EACT,CAAE,KAAMqD,CAAS,EAAID,EAC3B,IAAIE,EACAD,IAAa,aACfC,EAAQtD,EAAO,MAAM,EAErBsD,EAAQ,CACN,KAAM,IACN,KAAM,YACR,EAEF,MAAMC,EAAa,CAAC,EACpB,KAAOvD,EAAO,QAAQ,CACpB,KAAM,CAACgB,CAAI,EAAIhB,EACT,CAAE,KAAMwD,CAAS,EAAIxC,EAC3B,GAAIwC,IAAa,aACf,MAEAD,EAAW,KAAKvD,EAAO,MAAM,CAAC,CAElC,CACA,MAAMyD,EAAO,CACX,MAAAH,EACA,OAAQC,CACV,EACM3C,EAAQ,KAAK,iBAAiB6C,EAAMnE,EAAM,CAC9C,IAAKnB,CACP,CAAC,EACD,GAAIyC,EAAM,KACR,GAAIZ,EAAO,QACT,UAAW0D,KAAY9C,EAGrB,GAFAe,EACE,KAAK,oBAAoB,OAAO,OAAO,CAAC,EAAG3B,CAAM,EAAG0D,CAAQ,EAC1D/B,EACF,WAIJA,EAAO,EAGb,CACA,MAAO,CAAC,CAACA,CACX,CAQA,wBAAwBgC,EAASrE,EAAM,CACrC,KAAM,CACJ,QAAAiD,EAAU,GAAI,SAAA7B,EAAW,CAAC,EAAG,SAAArB,EAAW,GAAI,aAAAuE,EAAe,CAAC,CAC9D,EAAID,EACJ,IAAInD,EACJ,GAAI+B,IAAY,MACd,GAAIlD,EAAS,SAAS,OAAO,EAC3BmB,EAAM,SACD,CACL,IAAImB,EACJ,UAAW3B,KAAUU,EAEnB,GADAiB,EAAO,KAAK,oBAAoB,OAAO,OAAO,CAAC,EAAG3B,CAAM,EAAGV,CAAI,EAC3DqC,EACF,MAGAA,IACFnB,EAAMlB,EAEV,KACK,CACL,MAAMkD,EAAU,iBAAiB,KAAKD,CAAO,EACvCd,EAAImC,EAAa,OACvB,IAAIjC,EACJ,QAASd,EAAI,EAAGA,EAAIY,EAAGZ,IAAK,CAC1B,MAAME,EAAS6C,EAAa/C,CAAC,EACvBgD,EAAY9C,EAAO,OAAS,EAC5B,CAAE,OAAAf,CAAO,EAAIe,EAAO8C,CAAS,EAEnC,GADAlC,EAAO,KAAK,aAAa3B,EAAQV,EAAM,CAAE,QAAAkD,CAAQ,CAAC,EAC9Cb,GAAQkC,EAAY,EAAG,CACzB,IAAIC,EAAY,IAAI,IAAI,CAACxE,CAAI,CAAC,EAC9B,QAASuC,EAAIgC,EAAY,EAAGhC,GAAK,EAAGA,IAAK,CACvC,MAAM4B,EAAO1C,EAAOc,CAAC,EACf5B,EAAM,CAAC,EACb,UAAWyD,KAAYI,EAAW,CAChC,MAAMhC,EAAI,KAAK,iBAAiB2B,EAAMC,EAAU,CAC9C,QAAAlB,EACA,IAAKpE,CACP,CAAC,EACG0D,EAAE,MACJ7B,EAAI,KAAK,GAAG6B,CAAC,CAEjB,CACA,GAAI7B,EAAI,OACF4B,IAAM,EACRF,EAAO,GAEPmC,EAAY,IAAI,IAAI7D,CAAG,MAEpB,CACL0B,EAAO,GACP,KACF,CACF,CACF,CACA,GAAIA,EACF,KAEJ,CACIY,IAAY,MACTZ,IACHnB,EAAMlB,GAECqC,IACTnB,EAAMlB,EAEV,CACA,OAAOkB,GAAO,IAChB,CAWA,0BAA0BG,EAAKrB,EAAMC,EAAM,CAAC,EAAG,CAC7C,KAAM,CAAE,SAAUwE,CAAY,EAAIpD,EAC5B,CAAE,UAAAoB,EAAW,WAAAT,CAAW,EAAIhC,EAC5B,CAAE,QAAAkD,CAAQ,EAAIjD,EACdgD,KAAU,oBAAiB5B,EAAI,IAAI,EACzC,IAAIY,EAAU,IAAI,IAElB,GAAI,qBAAmB,KAAKgB,CAAO,EAAG,CACpC,IAAIoB,EACJ,GAAI,KAAKhF,GAAO,IAAIgC,CAAG,EACrBgD,EAAU,KAAKhF,GAAO,IAAIgC,CAAG,MACxB,CACL,MAAMD,KAAW,WAAQC,CAAG,EACtBqD,EAAY,CAAC,EACbJ,EAAe,CAAC,EACtB,SAAW,CAAC,GAAG5D,CAAM,IAAKU,EAAU,CAClC,UAAW0C,KAAQpD,EAAQ,CACzB,MAAMiE,KAAM,eAAYb,CAAI,EAC5BY,EAAU,KAAKC,CAAG,CACpB,CACA,MAAMlD,EAAS,CAAC,EACVmD,EAAY,IAAI,IACtB,IAAIlD,EAAOhB,EAAO,MAAM,EACxB,KAAOgB,GAUL,GATIA,EAAK,OAAS,cAChBD,EAAO,KAAK,CACV,MAAOC,EACP,OAAQ,CAAC,GAAGkD,CAAS,CACvB,CAAC,EACDA,EAAU,MAAM,GACPlD,GACTkD,EAAU,IAAIlD,CAAI,EAEhBhB,EAAO,OACTgB,EAAOhB,EAAO,MAAM,MACf,CACLe,EAAO,KAAK,CACV,MAAO,KACP,OAAQ,CAAC,GAAGmD,CAAS,CACvB,CAAC,EACDA,EAAU,MAAM,EAChB,KACF,CAEFN,EAAa,KAAK7C,CAAM,CAC1B,CACA4C,EAAU,CACR,QAAApB,EACA,SAAA7B,EACA,aAAAkD,EACA,SAAUI,EAAU,KAAK,GAAG,CAC9B,EACA,KAAKrF,GAAO,IAAIgC,EAAKgD,CAAO,CAC9B,CACA,MAAMnD,EAAM,KAAK,wBAAwBmD,EAASrE,CAAI,EAClDkB,GACFe,EAAQ,IAAIf,CAAG,CAEnB,SAAW,MAAM,QAAQuD,CAAW,EAAG,CACrC,KAAM,CAAChD,CAAM,EAAIgD,EAEjB,GAAI,oCAAoC,KAAKxB,CAAO,EAAG,CACrD,MAAM3B,EAAQ,KAAK,cAAcG,EAAQzB,EAAMiD,CAAO,EAClD3B,EAAM,OACRW,EAAUX,EAGd,SAAW2B,IAAY,MAAO,CAC5B,MAAM/B,EAAM,KAAK,2BAA2BO,EAAQzB,CAAI,EACpDkB,GACFe,EAAQ,IAAIf,CAAG,CAGnB,SAAW+B,IAAY,OAAQ,CAC7B,MAAM/B,EAAM,KAAK,0BAA0BO,EAAQzB,CAAI,EACnDkB,GACFe,EAAQ,IAAIf,CAAG,CAEnB,KACE,QAAQ+B,EAAS,CACf,IAAK,UACL,IAAK,UACL,IAAK,eAAgB,CACnB,GAAI,KAAKpD,GAAO,CACd,MAAMU,EAAM,6BAA6B0C,CAAO,KAChD,MAAM,IAAI,aAAa1C,EAAK,mBAAiB,CAC/C,CACA,KACF,CACA,IAAK,OACL,IAAK,eAEH,MAEF,QACE,GAAI,CAAC2C,EAAS,CACZ,MAAM3C,EAAM,yBAAyB0C,CAAO,KAC5C,MAAM,IAAI,aAAa1C,EAAK,YAAU,CACxC,CAEJ,CAEJ,KAAO,CACL,MAAMsE,EAAY,cACZC,EACJ,iEACIC,EAAkB,6CAClBC,EAAc,sBACdC,EAAe,uBACfC,EAAc,4CACdC,EACJ,2DACIC,EAAc,oDACpB,OAAQnC,EAAS,CACf,IAAK,WACL,IAAK,OAAQ,CACP4B,EAAU,KAAKpC,CAAS,GAAKzC,EAAK,aAAa,MAAM,GACvDiC,EAAQ,IAAIjC,CAAI,EAElB,KACF,CACA,IAAK,aAAc,CACjB,GAAI6E,EAAU,KAAKpC,CAAS,GAAKzC,EAAK,aAAa,MAAM,EAAG,CAC1D,KAAM,CAAE,KAAAqF,EAAM,OAAAC,EAAQ,SAAAC,CAAS,EAAI,IAAI,IAAI,KAAKjG,GAAU,GAAG,EACvDkG,EAAU,IAAI,IAAIxF,EAAK,aAAa,MAAM,EAAGqF,CAAI,EACnDG,EAAQ,SAAWF,GAAUE,EAAQ,WAAaD,GACpDtD,EAAQ,IAAIjC,CAAI,CAEpB,CACA,KACF,CACA,IAAK,UAEH,MAEF,IAAK,SAAU,CACb,KAAM,CAAE,KAAAyF,CAAK,EAAI,IAAI,IAAI,KAAKnG,GAAU,GAAG,EACvCU,EAAK,IAAMyF,IAAS,IAAIzF,EAAK,EAAE,IAC/B,KAAKV,GAAU,SAASU,CAAI,GAC9BiC,EAAQ,IAAIjC,CAAI,EAElB,KACF,CACA,IAAK,gBAAiB,CACpB,KAAM,CAAE,KAAAyF,CAAK,EAAI,IAAI,IAAI,KAAKnG,GAAU,GAAG,EAC3C,GAAImG,EAAM,CACR,MAAMC,EAAKD,EAAK,QAAQ,KAAM,EAAE,EAChC,IAAI7D,EAAU,KAAKtC,GAAU,eAAeoG,CAAE,EAC9C,KAAO9D,GAAS,CACd,GAAIA,IAAY5B,EAAM,CACpBiC,EAAQ,IAAIjC,CAAI,EAChB,KACF,CACA4B,EAAUA,EAAQ,UACpB,CACF,CACA,KACF,CACA,IAAK,QAAS,CACR,KAAKpC,GAAM,WAAa,eACtBQ,IAAS,KAAKR,IAChByC,EAAQ,IAAIjC,CAAI,EAETA,IAAS,KAAKV,GAAU,iBACjC2C,EAAQ,IAAIjC,CAAI,EAElB,KACF,CACA,IAAK,QAAS,CACRA,IAAS,KAAKV,GAAU,eAC1B2C,EAAQ,IAAIjC,CAAI,EAElB,KACF,CACA,IAAK,eAAgB,CACnB,IAAI4B,EAAU,KAAKtC,GAAU,cAC7B,KAAOsC,GAAS,CACd,GAAIA,IAAY5B,EAAM,CACpBiC,EAAQ,IAAIjC,CAAI,EAChB,KACF,CACA4B,EAAUA,EAAQ,UACpB,CACA,KACF,CACA,IAAK,OAAQ,CACPoD,EAAY,KAAKvC,CAAS,GAAKzC,EAAK,aAAa,MAAM,GACzDiC,EAAQ,IAAIjC,CAAI,EAElB,KACF,CACA,IAAK,SAAU,CACTgF,EAAY,KAAKvC,CAAS,GAAK,CAACzC,EAAK,aAAa,MAAM,GAC1DiC,EAAQ,IAAIjC,CAAI,EAElB,KACF,CACA,IAAK,WAAY,CACf,GAAI8E,EAAY,KAAKrC,CAAS,MAAK,EAAAkD,SAAoBlD,CAAS,EAC9D,GAAIzC,EAAK,UAAYA,EAAK,aAAa,UAAU,EAC/CiC,EAAQ,IAAIjC,CAAI,MACX,CACL,IAAIM,EAAS0B,EACb,KAAO1B,GACDA,EAAO,YAAc,YAGzBA,EAASA,EAAO,WAEdA,GAAU0B,EAAW,YAAc,UACnC1B,EAAO,aAAa,UAAU,GAChC2B,EAAQ,IAAIjC,CAAI,CAEpB,CAEF,KACF,CACA,IAAK,UAAW,EACT8E,EAAY,KAAKrC,CAAS,MAAK,EAAAkD,SAAoBlD,CAAS,IAC7D,EAAEzC,EAAK,UAAYA,EAAK,aAAa,UAAU,IACjDiC,EAAQ,IAAIjC,CAAI,EAElB,KACF,CACA,IAAK,YAAa,CAChB,OAAQyC,EAAW,CACjB,IAAK,WAAY,EACXzC,EAAK,UAAYA,EAAK,aAAa,UAAU,GAC7CA,EAAK,UAAYA,EAAK,aAAa,UAAU,IAC/CiC,EAAQ,IAAIjC,CAAI,EAElB,KACF,CACA,IAAK,QAAS,EACP,CAACA,EAAK,MAAQkF,EAAY,KAAKlF,EAAK,IAAI,GACxCoF,EAAY,KAAKpF,EAAK,IAAI,KAC1BA,EAAK,UAAYA,EAAK,aAAa,UAAU,GAC7CA,EAAK,UAAYA,EAAK,aAAa,UAAU,IAChDiC,EAAQ,IAAIjC,CAAI,EAElB,KACF,CACA,WACO,qBAAkBA,CAAI,GACzBiC,EAAQ,IAAIjC,CAAI,CAGtB,CACA,KACF,CACA,IAAK,aAAc,CACjB,OAAQyC,EAAW,CACjB,IAAK,WAAY,CACTzC,EAAK,UAAYA,EAAK,aAAa,UAAU,GAC7CA,EAAK,UAAYA,EAAK,aAAa,UAAU,GACjDiC,EAAQ,IAAIjC,CAAI,EAElB,KACF,CACA,IAAK,QAAS,EACP,CAACA,EAAK,MAAQkF,EAAY,KAAKlF,EAAK,IAAI,GACxCoF,EAAY,KAAKpF,EAAK,IAAI,IAC3B,EAAEA,EAAK,UAAYA,EAAK,aAAa,UAAU,GAC7CA,EAAK,UAAYA,EAAK,aAAa,UAAU,IACjDiC,EAAQ,IAAIjC,CAAI,EAElB,KACF,CACA,WACM,qBAAkBA,CAAI,GACxBiC,EAAQ,IAAIjC,CAAI,CAGtB,CACA,KACF,CACA,IAAK,oBAAqB,CACxB,IAAI4F,EACAnD,IAAc,WAChBmD,EAAa5F,EACJyC,IAAc,UACnBzC,EAAK,aAAa,MAAM,EACtBoF,EAAY,KAAKpF,EAAK,aAAa,MAAM,CAAC,IAC5C4F,EAAa5F,GAGf4F,EAAa5F,GAGb4F,GAAc5F,EAAK,QAAU,IAC7BA,EAAK,aAAa,aAAa,GAC/BA,EAAK,aAAa,aAAa,EAAE,KAAK,EAAE,QAC1CiC,EAAQ,IAAIjC,CAAI,EAElB,KACF,CACA,IAAK,UAAW,EACTA,EAAK,SAAWyC,IAAc,SAC9BzC,EAAK,aAAa,MAAM,GACxBiF,EAAa,KAAKjF,EAAK,aAAa,MAAM,CAAC,GAC3CA,EAAK,UAAYyC,IAAc,WAClCR,EAAQ,IAAIjC,CAAI,EAElB,KACF,CACA,IAAK,gBAAiB,CACpB,GAAKA,EAAK,eAAiByC,IAAc,SACpCzC,EAAK,OAAS,YACdyC,IAAc,YAAc,CAACzC,EAAK,aAAa,OAAO,EACzDiC,EAAQ,IAAIjC,CAAI,UACPyC,IAAc,SAAWzC,EAAK,OAAS,SACvC,CAACA,EAAK,aAAa,SAAS,EAAG,CACxC,MAAM6F,EAAW7F,EAAK,KACtB,IAAIM,EAASN,EAAK,WAClB,KAAOM,GACDA,EAAO,YAAc,QAGzBA,EAASA,EAAO,WAEbA,IACHA,EAAS,KAAKhB,GAAU,iBAE1B,IAAIwG,EACJ,MAAMxE,EAAQ,CAAC,EAAE,MAAM,KAAKhB,EAAO,qBAAqB,OAAO,CAAC,EAChE,UAAWoB,KAAQJ,EACjB,GAAII,EAAK,aAAa,MAAM,IAAM,UAC5BmE,EACEnE,EAAK,aAAa,MAAM,IAAMmE,IAChCC,EAAU,CAAC,CAACpE,EAAK,SAETA,EAAK,aAAa,MAAM,IAClCoE,EAAU,CAAC,CAACpE,EAAK,SAEfoE,GACF,MAIDA,GACH7D,EAAQ,IAAIjC,CAAI,CAEpB,CACA,KACF,CACA,IAAK,UAAW,CACd,MAAM+F,EAAe,qBACfC,EAAgB,qBAEtB,GAAKvD,IAAc,UACd,EAAEzC,EAAK,aAAa,MAAM,GACxB+F,EAAa,KAAK/F,EAAK,aAAa,MAAM,CAAC,IAC7CyC,IAAc,SAAWzC,EAAK,aAAa,MAAM,GACjDgG,EAAc,KAAKhG,EAAK,aAAa,MAAM,CAAC,EAAI,CACnD,IAAIiG,EAAOjG,EAAK,WAChB,KAAOiG,GACDA,EAAK,YAAc,QAGvBA,EAAOA,EAAK,WAEd,GAAIA,EAAM,CACR,MAAMxF,EACJ,KAAKnB,GAAU,iBAAiB2G,EAAM,cAAY,EACpD,IAAI7B,EAAW3D,EAAO,WAAW,EACjC,KAAO2D,GAAU,CACf,MAAMyB,EAAWzB,EAAS,UAC1B,IAAI5B,EAQJ,GAPIqD,IAAa,SACfrD,EAAI,EAAE4B,EAAS,aAAa,MAAM,GAChC2B,EAAa,KAAK3B,EAAS,aAAa,MAAM,CAAC,GACxCyB,IAAa,UACtBrD,EAAI4B,EAAS,aAAa,MAAM,GAC9B4B,EAAc,KAAK5B,EAAS,aAAa,MAAM,CAAC,GAEhD5B,EAAG,CACD4B,IAAapE,GACfiC,EAAQ,IAAIjC,CAAI,EAElB,KACF,CACAoE,EAAW3D,EAAO,SAAS,CAC7B,CACF,CAEF,SAAWgC,IAAc,SAAWzC,EAAK,aAAa,MAAM,GACjDiF,EAAa,KAAKjF,EAAK,aAAa,MAAM,CAAC,IAC1CA,EAAK,SAAWA,EAAK,aAAa,SAAS,GACrDiC,EAAQ,IAAIjC,CAAI,UAEPyC,IAAc,SAAU,CACjC,IAAIyD,EAAa,GACb5F,EAAS0B,EACb,KAAO1B,GACDA,EAAO,YAAc,YADZ,CAGN,GAAIA,EAAO,YAAc,SAAU,EACpCA,EAAO,UAAYA,EAAO,aAAa,UAAU,KACnD4F,EAAa,IAEf,KACF,CACA5F,EAASA,EAAO,UAClB,CACA,GAAI4F,GACElG,EAAK,UAAYA,EAAK,aAAa,UAAU,IAC/CiC,EAAQ,IAAIjC,CAAI,MAEb,CACL,MAAMmG,EAAa,IAAI,IACjB1F,EACJ,KAAKnB,GAAU,iBAAiB0C,EAAY,cAAY,EAC1D,IAAIH,EAAUpB,EAAO,WAAW,EAChC,KAAOoB,GAAS,CACd,GAAIA,EAAQ,UAAYA,EAAQ,aAAa,UAAU,EAAG,CACxDsE,EAAW,IAAItE,CAAO,EACtB,KACF,CACAA,EAAUpB,EAAO,YAAY,CAC/B,CACI0F,EAAW,MACTA,EAAW,IAAInG,CAAI,GACrBiC,EAAQ,IAAIjC,CAAI,CAGtB,CACF,CACA,KACF,CACA,IAAK,QAAS,CACZ,GAAI+E,EAAgB,KAAKtC,CAAS,EAC5BzC,EAAK,cAAc,GACrBiC,EAAQ,IAAIjC,CAAI,UAETyC,IAAc,WAAY,CACnC,IAAIJ,EACJ,MAAM5B,EAAS,KAAKnB,GAAU,iBAAiBU,EAAM,cAAY,EACjE,IAAI6B,EAAUpB,EAAO,WAAW,EAChC,KAAOoB,GACD,EAAAkD,EAAgB,KAAKlD,EAAQ,SAAS,IACxCQ,EAAOR,EAAQ,cAAc,EACzB,CAACQ,KAIPR,EAAUpB,EAAO,SAAS,EAExB4B,GACFJ,EAAQ,IAAIjC,CAAI,CAEpB,CACA,KACF,CACA,IAAK,UAAW,CACd,GAAI+E,EAAgB,KAAKtC,CAAS,EAC3BzC,EAAK,cAAc,GACtBiC,EAAQ,IAAIjC,CAAI,UAETyC,IAAc,WAAY,CACnC,IAAIJ,EACJ,MAAM5B,EAAS,KAAKnB,GAAU,iBAAiBU,EAAM,cAAY,EACjE,IAAI6B,EAAUpB,EAAO,WAAW,EAChC,KAAOoB,GACD,EAAAkD,EAAgB,KAAKlD,EAAQ,SAAS,IACxCQ,EAAOR,EAAQ,cAAc,EACzB,CAACQ,KAIPR,EAAUpB,EAAO,SAAS,EAEvB4B,GACHJ,EAAQ,IAAIjC,CAAI,CAEpB,CACA,KACF,CACA,IAAK,WAAY,CACXyC,IAAc,SACd,EAAEzC,EAAK,UAAYA,EAAK,aAAa,UAAU,IAC/C,EAAEA,EAAK,UAAYA,EAAK,aAAa,UAAU,IAC/CA,EAAK,aAAa,MAAM,GACxBmF,EAAa,KAAKnF,EAAK,aAAa,MAAM,CAAC,GAC3C,EAAEA,EAAK,SAAS,gBACdA,EAAK,SAAS,iBACfA,EAAK,aAAa,KAAK,GAAKA,EAAK,aAAa,KAAK,GACnDA,EAAK,aAAa,MAAM,IAAM,UACjCiC,EAAQ,IAAIjC,CAAI,EAElB,KACF,CACA,IAAK,eAAgB,CACfyC,IAAc,SACd,EAAEzC,EAAK,UAAYA,EAAK,aAAa,UAAU,IAC/C,EAAEA,EAAK,UAAYA,EAAK,aAAa,UAAU,IAC/CA,EAAK,aAAa,MAAM,GACxBmF,EAAa,KAAKnF,EAAK,aAAa,MAAM,CAAC,IAC1CA,EAAK,SAAS,gBAAkBA,EAAK,SAAS,gBACjDiC,EAAQ,IAAIjC,CAAI,EAElB,KACF,CACA,IAAK,WAAY,CACf,IAAI4F,EACJ,GAAI,wBAAwB,KAAKnD,CAAS,EACxCmD,EAAa5F,UACJyC,IAAc,QACvB,GAAIzC,EAAK,aAAa,MAAM,EAAG,CAC7B,MAAMoG,EAAYpG,EAAK,aAAa,MAAM,GACtCoG,IAAc,QAAUnB,EAAa,KAAKmB,CAAS,GACnDlB,EAAY,KAAKkB,CAAS,GAAKhB,EAAY,KAAKgB,CAAS,KAC3DR,EAAa5F,EAEjB,MACE4F,EAAa5F,EAGb4F,IACC5F,EAAK,UAAYA,EAAK,aAAa,UAAU,IAChDiC,EAAQ,IAAIjC,CAAI,EAElB,KACF,CACA,IAAK,WAAY,CACf,IAAI4F,EACJ,GAAI,wBAAwB,KAAKnD,CAAS,EACxCmD,EAAa5F,UACJyC,IAAc,QACvB,GAAIzC,EAAK,aAAa,MAAM,EAAG,CAC7B,MAAMoG,EAAYpG,EAAK,aAAa,MAAM,GACtCoG,IAAc,QAAUnB,EAAa,KAAKmB,CAAS,GACnDlB,EAAY,KAAKkB,CAAS,GAAKhB,EAAY,KAAKgB,CAAS,KAC3DR,EAAa5F,EAEjB,MACE4F,EAAa5F,EAGb4F,GACA,EAAE5F,EAAK,UAAYA,EAAK,aAAa,UAAU,IACjDiC,EAAQ,IAAIjC,CAAI,EAElB,KACF,CACA,IAAK,OAAQ,CACPA,IAAS,KAAKV,GAAU,iBAC1B2C,EAAQ,IAAIjC,CAAI,EAElB,KACF,CACA,IAAK,QAAS,CACZ,GAAIA,EAAK,cAAc,EAAG,CACxB,IAAIqC,EACJ,MAAM5B,EAAS,KAAKnB,GAAU,iBAAiBU,EAAM,UAAQ,EAC7D,IAAI6B,EAAUpB,EAAO,WAAW,EAChC,KAAOoB,IACLQ,EAAOR,EAAQ,WAAa,gBAC1BA,EAAQ,WAAa,YACnB,EAACQ,IAGLR,EAAUpB,EAAO,YAAY,EAE3B4B,GACFJ,EAAQ,IAAIjC,CAAI,CAEpB,MACEiC,EAAQ,IAAIjC,CAAI,EAElB,KACF,CACA,IAAK,cAAe,EACbgC,GAAchC,IAASgC,EAAW,mBAClChC,IAAS,KAAKN,IAAS,KAAKA,GAAM,WAAa,iBAClDuC,EAAQ,IAAIjC,CAAI,EAElB,KACF,CACA,IAAK,aAAc,EACZgC,GAAchC,IAASgC,EAAW,kBAClChC,IAAS,KAAKN,IAAS,KAAKA,GAAM,WAAa,iBAClDuC,EAAQ,IAAIjC,CAAI,EAElB,KACF,CACA,IAAK,aAAc,EACZgC,GACAhC,IAASgC,EAAW,mBACpBhC,IAASgC,EAAW,kBACpBhC,IAAS,KAAKN,IAAS,KAAKA,GAAM,WAAa,iBAClDuC,EAAQ,IAAIjC,CAAI,EAElB,KACF,CACA,IAAK,gBAAiB,CACpB,GAAIgC,EAAY,CACd,KAAM,CAACqE,CAAK,EAAI,KAAK,kBAAkB,CACrC,EAAG,EACH,EAAG,CACL,EAAGrG,CAAI,EACHqG,GACFpE,EAAQ,IAAIoE,CAAK,CAErB,MAAWrG,IAAS,KAAKN,IACd,KAAKA,GAAM,WAAa,gBACjCuC,EAAQ,IAAIjC,CAAI,EAElB,KACF,CACA,IAAK,eAAgB,CACnB,GAAIgC,EAAY,CACd,KAAM,CAACqE,CAAK,EAAI,KAAK,kBAAkB,CACrC,EAAG,EACH,EAAG,EACH,QAAS,EACX,EAAGrG,CAAI,EACHqG,GACFpE,EAAQ,IAAIoE,CAAK,CAErB,MAAWrG,IAAS,KAAKN,IACd,KAAKA,GAAM,WAAa,gBACjCuC,EAAQ,IAAIjC,CAAI,EAElB,KACF,CACA,IAAK,eAAgB,CACnB,GAAIgC,EAAY,CACd,KAAM,CAACqE,CAAK,EAAI,KAAK,kBAAkB,CACrC,EAAG,EACH,EAAG,CACL,EAAGrG,CAAI,EACP,GAAIqG,IAAUrG,EAAM,CAClB,KAAM,CAACsG,CAAK,EAAI,KAAK,kBAAkB,CACrC,EAAG,EACH,EAAG,EACH,QAAS,EACX,EAAGtG,CAAI,EACHsG,IAAUtG,GACZiC,EAAQ,IAAIjC,CAAI,CAEpB,CACF,MAAWA,IAAS,KAAKN,IACd,KAAKA,GAAM,WAAa,gBACjCuC,EAAQ,IAAIjC,CAAI,EAElB,KACF,CACA,IAAK,OACL,IAAK,eAEH,MAGF,IAAK,QACL,IAAK,SACL,IAAK,eACL,IAAK,aAAc,CACjB,GAAI,KAAKH,GAAO,CACd,MAAMU,EAAM,gCAAgC0C,CAAO,GACnD,MAAM,IAAI,aAAa1C,EAAK,mBAAiB,CAC/C,CACA,KACF,CACA,IAAK,SACL,IAAK,WACL,IAAK,QACL,IAAK,YACL,IAAK,UACL,IAAK,UACL,IAAK,gBACL,IAAK,aACL,IAAK,SACL,IAAK,QACL,IAAK,QACL,IAAK,QACL,IAAK,OACL,IAAK,SACL,IAAK,qBACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,eACL,IAAK,aACL,IAAK,gBACL,IAAK,mBAAoB,CACvB,GAAI,KAAKV,GAAO,CACd,MAAMU,EAAM,6BAA6B0C,CAAO,GAChD,MAAM,IAAI,aAAa1C,EAAK,mBAAiB,CAC/C,CACA,KACF,CACA,QACE,GAAI0C,EAAQ,WAAW,UAAU,GAC/B,GAAI,KAAKpD,GAAO,CACd,MAAMU,EAAM,6BAA6B0C,CAAO,GAChD,MAAM,IAAI,aAAa1C,EAAK,mBAAiB,CAC/C,UACS,CAAC2C,EAAS,CACnB,MAAM3C,EAAM,yBAAyB0C,CAAO,GAC5C,MAAM,IAAI,aAAa1C,EAAK,YAAU,CACxC,CAEJ,CACF,CACA,OAAO0B,CACT,CAQA,wBAAwBZ,EAAKrB,EAAM,CACjC,KAAM,CACJ,MAAOuG,EAAU,QAASC,EAAY,KAAMvD,EAAS,MAAOwD,CAC9D,EAAIpF,EACJ,GAAI,OAAOkF,GAAa,UAAY,CAAC,UAAU,KAAKA,CAAQ,EAAG,CAE7D,MAAMhG,EAAM,uBADA,eAAYc,CAAG,CACQ,GACnC,MAAM,IAAI,aAAad,EAAK,YAAU,CACxC,CACA,KAAM,CAAE,WAAAmG,CAAW,EAAI1G,EACvB,IAAIkB,EACJ,GAAIwF,GAAcA,EAAW,OAAQ,CACnC,IAAIC,EACA,KAAKrH,GAAU,cAAgB,YAC7B,OAAOiH,GAAa,UAAY,OAAO,KAAKA,CAAQ,EACtDI,EAAkB,GAElBA,EAAkB,GAEX,OAAOJ,GAAa,UAAY,OAAO,KAAKA,CAAQ,EAC7DI,EAAkB,GAElBA,EAAkB,GAEpB,IAAIC,KAAc,oBAAiB3D,EAAQ,IAAI,EAC3C0D,IACFC,EAAcA,EAAY,YAAY,GAExC,MAAMC,EAAa,IAAI,IAEvB,GAAID,EAAY,QAAQ,GAAG,EAAI,GAAI,CACjC,KAAM,CACJ,OAAQE,EAAe,QAASC,CAClC,KAAI,uBAAoBH,CAAW,EACnC,OAAS,CAAE,KAAMI,EAAU,MAAOC,CAAU,IAAKP,EAK/C,OAJIC,IACFK,EAAWA,EAAS,YAAY,EAChCC,EAAYA,EAAU,YAAY,GAE5BH,EAAe,CACrB,IAAK,GAAI,CACHC,IAAqBC,GACvBH,EAAW,IAAII,CAAS,EAE1B,KACF,CACA,IAAK,IAAK,CACJD,EAAS,QAAQ,GAAG,EAAI,GACtBA,EAAS,SAAS,IAAID,CAAgB,EAAE,GAC1CF,EAAW,IAAII,CAAS,EAEjBF,IAAqBC,GAC9BH,EAAW,IAAII,CAAS,EAE1B,KACF,CACA,QACE,GAAID,EAAS,QAAQ,GAAG,EAAI,GAAI,CAC9B,KAAM,CAACE,EAAgBC,CAAiB,EAAIH,EAAS,MAAM,GAAG,EAC1DF,IAAkBI,GAClBH,IAAqBI,MACrB,uBAAoBL,EAAe9G,CAAI,GACzC6G,EAAW,IAAII,CAAS,CAE5B,CAEJ,CAEJ,KACE,QAAS,CAAE,KAAMD,EAAU,MAAOC,CAAU,IAAKP,EAK/C,GAJIC,IACFK,EAAWA,EAAS,YAAY,EAChCC,EAAYA,EAAU,YAAY,GAEhCD,EAAS,QAAQ,GAAG,EAAI,GAAI,CAC9B,KAAM,CAACE,EAAgBC,CAAiB,EAAIH,EAAS,MAAM,GAAG,EAE9D,GAAIE,IAAmB,OAASC,IAAsB,OACpD,SACSP,IAAgBO,GACzBN,EAAW,IAAII,CAAS,CAE5B,MAAWL,IAAgBI,GACzBH,EAAW,IAAII,CAAS,EAI9B,GAAIJ,EAAW,KAAM,CACnB,KAAM,CACJ,KAAMO,EAAmB,MAAOC,CAClC,EAAIZ,GAAY,CAAC,EACjB,IAAIa,EAgBJ,OAfIF,EACET,EACFW,EAAYF,EAAkB,YAAY,EAE1CE,EAAYF,EAELC,EACLV,EACFW,EAAYD,EAAmB,YAAY,EAE3CC,EAAYD,EAELA,IAAuB,KAChCC,EAAYD,GAENb,EAAY,CAClB,IAAK,IAAK,CACJ,OAAOc,GAAc,UAAYT,EAAW,IAAIS,CAAS,IAC3DpG,EAAMlB,GAER,KACF,CACA,IAAK,KAAM,CACT,GAAIsH,GAAa,OAAOA,GAAc,UACpC,UAAWzD,KAASgD,EAElB,GADa,IAAI,IAAIhD,EAAM,MAAM,KAAK,CAAC,EAC9B,IAAIyD,CAAS,EAAG,CACvBpG,EAAMlB,EACN,KACF,EAGJ,KACF,CACA,IAAK,KAAM,CACT,GAAIsH,GAAa,OAAOA,GAAc,SAAU,CAC9C,IAAI5F,EACJ,UAAWmC,KAASgD,EAClB,GAAIhD,IAAUyD,GAAazD,EAAM,WAAW,GAAGyD,CAAS,GAAG,EAAG,CAC5D5F,EAAOmC,EACP,KACF,CAEEnC,IACFR,EAAMlB,EAEV,CACA,KACF,CACA,IAAK,KAAM,CACT,GAAIsH,GAAa,OAAOA,GAAc,SAAU,CAC9C,IAAI5F,EACJ,UAAWmC,KAASgD,EAClB,GAAIhD,EAAM,WAAW,GAAGyD,CAAS,EAAE,EAAG,CACpC5F,EAAOmC,EACP,KACF,CAEEnC,IACFR,EAAMlB,EAEV,CACA,KACF,CACA,IAAK,KAAM,CACT,GAAIsH,GAAa,OAAOA,GAAc,SAAU,CAC9C,IAAI5F,EACJ,UAAWmC,KAASgD,EAClB,GAAIhD,EAAM,SAAS,GAAGyD,CAAS,EAAE,EAAG,CAClC5F,EAAOmC,EACP,KACF,CAEEnC,IACFR,EAAMlB,EAEV,CACA,KACF,CACA,IAAK,KAAM,CACT,GAAIsH,GAAa,OAAOA,GAAc,SAAU,CAC9C,IAAI5F,EACJ,UAAWmC,KAASgD,EAClB,GAAIhD,EAAM,SAAS,GAAGyD,CAAS,EAAE,EAAG,CAClC5F,EAAOmC,EACP,KACF,CAEEnC,IACFR,EAAMlB,EAEV,CACA,KACF,CACA,KAAK,KACL,QACEkB,EAAMlB,CAEV,CACF,CACF,CACA,OAAOkB,GAAO,IAChB,CAQA,oBAAoBG,EAAKrB,EAAM,CAC7B,MAAMiD,KAAU,oBAAiB5B,EAAI,IAAI,EACzC,IAAIH,EACJ,OAAIlB,EAAK,UAAU,SAASiD,CAAO,IACjC/B,EAAMlB,GAEDkB,GAAO,IAChB,CAQA,iBAAiBG,EAAKrB,EAAM,CAC1B,MAAMiD,KAAU,oBAAiB5B,EAAI,IAAI,EACnC,CAAE,GAAAqE,CAAG,EAAI1F,EACf,IAAIkB,EACJ,OAAI+B,IAAYyC,IACdxE,EAAMlB,GAEDkB,GAAO,IAChB,CAUA,mBAAmBG,EAAKrB,EAAMC,EAAM,CAAC,EAAG,CACtC,MAAMgD,KAAU,oBAAiB5B,EAAI,IAAI,EACnC,CAAE,UAAAoB,EAAW,OAAAC,CAAO,EAAI1C,EACxB,CAAE,QAAAkD,CAAQ,EAAIjD,EACpB,GAAI,CACF,OAAQsH,EAAW,QAASC,CAC9B,KAAI,uBAAoBvE,EAASjD,CAAI,EACjC,KAAKV,GAAU,cAAgB,cACjCiI,EAAYA,EAAU,YAAY,EAClCC,EAAcA,EAAY,YAAY,GAExC,IAAIC,EACA5B,EAEApD,EAAU,QAAQ,GAAG,EAAI,GAC3B,CAACgF,EAAY5B,CAAQ,EAAIpD,EAAU,MAAM,GAAG,GAE5CgF,EAAa/E,GAAU,GACvBmD,EAAWpD,GAEb,IAAIvB,EACJ,GAAIqG,IAAc,IAAME,IAAe,GACjCzH,EAAK,eAAiB,OACrBwH,IAAgB,KAAOA,IAAgB3B,KAC1C3E,EAAMlB,WAECuH,IAAc,KACnBC,IAAgB,KAAOA,IAAgB3B,KACzC3E,EAAMlB,WAECuH,IAAcE,GACvB,MAAI,uBAAoBF,EAAWvH,CAAI,GACjCwH,IAAgB,KAAOA,IAAgB3B,KACzC3E,EAAMlB,WAEC,CAACkD,EAAS,CACnB,MAAM3C,EAAM,wBAAwBgH,CAAS,GAC7C,MAAM,IAAI,aAAahH,EAAK,YAAU,CACxC,UACSgH,GAAa,CAACrE,GAAW,IAAC,uBAAoBqE,EAAWvH,CAAI,EAAG,CACzE,MAAMO,EAAM,wBAAwBgH,CAAS,GAC7C,MAAM,IAAI,aAAahH,EAAK,YAAU,CACxC,CACA,OAAOW,GAAO,IAChB,CAQA,4BAA4BG,EAAKrB,EAAM,CACrC,KAAM,CAAE,SAAUyE,CAAY,EAAIpD,EAC5B4B,KAAU,oBAAiB5B,EAAI,IAAI,EACzC,IAAIH,EACJ,GAAI,MAAM,QAAQuD,CAAW,EAAG,CAC9B,KAAM,CAAChD,CAAM,KAAI,WAAQgD,EAAY,CAAC,CAAC,EACjC,CAAC,GAAG/D,CAAM,EAAIe,EACd,CAAE,KAAAiG,CAAK,EAAI1H,EACjB,GAAIiD,IAAY,OAAQ,CACtB,IAAIZ,EACJ,UAAWyB,KAAQpD,EAAQ,CACzB,KAAM,CAAE,KAAMqD,CAAS,EAAID,EAC3B,GAAIC,IAAa,aAAY,CAE3B,MAAMxD,EAAM,uBADA,eAAYc,CAAG,CACQ,GACnC,MAAM,IAAI,aAAad,EAAK,YAAU,CACxC,CAEA,GADA8B,EAAO,KAAK,eAAeyB,EAAM4D,CAAI,EAAE,IAAIA,CAAI,EAC3C,CAACrF,EACH,KAEJ,CACIA,IACFnB,EAAMlB,EAEV,SAAWiD,IAAY,eAAgB,CACrC,IAAIZ,EACA/B,EAASoH,EACb,KAAOpH,GAAQ,CACb,UAAWwD,KAAQpD,EAAQ,CACzB,KAAM,CAAE,KAAMqD,CAAS,EAAID,EAC3B,GAAIC,IAAa,aAAY,CAE3B,MAAMxD,EAAM,uBADA,eAAYc,CAAG,CACQ,GACnC,MAAM,IAAI,aAAad,EAAK,YAAU,CACxC,CAEA,GADA8B,EAAO,KAAK,eAAeyB,EAAMxD,CAAM,EAAE,IAAIA,CAAM,EAC/C,CAAC+B,EACH,KAEJ,CACA,GAAIA,EACF,MAEA/B,EAASA,EAAO,UAEpB,CACI+B,IACFnB,EAAMlB,EAEV,CACF,SAAWiD,IAAY,OACrB/B,EAAMlB,MACD,CACL,MAAMO,EAAM,qBAAqB0C,CAAO,GACxC,MAAM,IAAI,aAAa1C,EAAK,YAAU,CACxC,CACA,OAAOW,GAAO,IAChB,CASA,eAAeG,EAAKrB,EAAMC,EAAK,CAC7B,KAAM,CAAE,KAAM0H,CAAQ,EAAItG,EACpB4B,KAAU,oBAAiB5B,EAAI,IAAI,EACzC,IAAIY,EAAU,IAAI,IAClB,GAAIjC,EAAK,WAAa,eACpB,OAAQ2H,EAAS,CACf,KAAK,gBAAe,CAClB,MAAMzG,EAAM,KAAK,wBAAwBG,EAAKrB,CAAI,EAC9CkB,GACFe,EAAQ,IAAIf,CAAG,EAEjB,KACF,CACA,KAAK,iBAAgB,CACnB,MAAMA,EAAM,KAAK,oBAAoBG,EAAKrB,CAAI,EAC1CkB,GACFe,EAAQ,IAAIf,CAAG,EAEjB,KACF,CACA,KAAK,cAAa,CAChB,MAAMA,EAAM,KAAK,iBAAiBG,EAAKrB,CAAI,EACvCkB,GACFe,EAAQ,IAAIf,CAAG,EAEjB,KACF,CACA,KAAK,wBAAuB,CAC1B,MAAMI,EAAQ,KAAK,0BAA0BD,EAAKrB,EAAMC,CAAG,EACvDqB,EAAM,OACRW,EAAUX,GAEZ,KACF,CACA,KAAK,0BAAyB,CAC5B,KAAK,4BAA4B2B,EAAShD,CAAG,EAC7C,KACF,CACA,KAAK,gBACL,QAAS,CACP,MAAMiB,EAAM,KAAK,mBAAmBG,EAAKrB,EAAMC,CAAG,EAC9CiB,GACFe,EAAQ,IAAIf,CAAG,CAEnB,CACF,SACS,KAAKvB,IAAWgI,IAAY,yBAC5B3H,EAAK,WAAa,0BAC3B,GAAIiD,IAAY,OAAS,qBAAmB,KAAKA,CAAO,EAAG,CACzD,MAAM3B,EAAQ,KAAK,0BAA0BD,EAAKrB,EAAMC,CAAG,EACvDqB,EAAM,OACRW,EAAUX,EAEd,SAAW,kBAAgB,KAAK2B,CAAO,EAAG,CACxC,MAAM/B,EAAM,KAAK,4BAA4BG,EAAKrB,CAAI,EAClDkB,GACFe,EAAQ,IAAIf,CAAG,CAEnB,EAEF,OAAOe,CACT,CASA,aAAavB,EAAQV,EAAMC,EAAK,CAC9B,IAAIoC,EACJ,UAAWyB,KAAQpD,EAEjB,GADA2B,EAAO,KAAK,eAAeyB,EAAM9D,EAAMC,CAAG,EAAE,IAAID,CAAI,EAChD,CAACqC,EACH,MAGJ,MAAO,CAAC,CAACA,CACX,CAQA,qBAAqB3B,EAAQkH,EAAU,CACrC,KAAM,CAAC9D,EAAM,GAAG+D,CAAY,EAAInH,EAC1B,CAAE,KAAMqD,CAAS,EAAID,EACrBgE,KAAW,oBAAiBhE,EAAK,IAAI,EACrCiE,EAAWF,EAAa,OAAS,EACvC,IAAIvG,EAAQ,IAAI,IACZ0G,EAAU,GACd,GAAI,KAAKrI,GACPqI,EAAU,OAEV,QAAQjE,EAAU,CAChB,KAAK,cAAa,CAChB,GAAI,KAAKrE,GAAM,WAAa,eAC1BsI,EAAU,OACL,CACL,MAAMhI,EAAO,KAAKN,GAAM,eAAeoI,CAAQ,EAC3C9H,GAAQA,IAAS4H,GAAYA,EAAS,SAAS5H,CAAI,IACjD+H,EACW,KAAK,aAAaF,EAAc7H,CAAI,GAE/CsB,EAAM,IAAItB,CAAI,EAGhBsB,EAAM,IAAItB,CAAI,EAGpB,CACA,KACF,CACA,KAAK,iBAAgB,CACnB,MAAMW,EAAM,CAAC,EAAE,MAAM,KAAKiH,EAAS,uBAAuBE,CAAQ,CAAC,EACnE,GAAInH,EAAI,OACN,GAAIoH,EACF,UAAW/H,KAAQW,EACJ,KAAK,aAAakH,EAAc7H,CAAI,GAE/CsB,EAAM,IAAItB,CAAI,OAIlBsB,EAAQ,IAAI,IAAIX,CAAG,EAGvB,KACF,CACA,KAAK,gBAAe,CAClB,GAAI,KAAKrB,GAAU,cAAgB,aAC/B,CAAC,OAAO,KAAKwI,CAAQ,EAAG,CAC1B,MAAMnH,EAAM,CAAC,EAAE,MAAM,KAAKiH,EAAS,qBAAqBE,CAAQ,CAAC,EACjE,GAAInH,EAAI,OACN,GAAIoH,EACF,UAAW/H,KAAQW,EACJ,KAAK,aAAakH,EAAc7H,CAAI,GAE/CsB,EAAM,IAAItB,CAAI,OAIlBsB,EAAQ,IAAI,IAAIX,CAAG,CAGzB,MACEqH,EAAU,GAEZ,KACF,CACA,KAAK,0BAAyB,CAC5B,KAAK,4BAA4BF,CAAQ,EACzC,KACF,CACA,QACEE,EAAU,EAEd,CAEF,MAAO,CACL,MAAA1G,EACA,QAAA0G,CACF,CACF,CAWA,iBAAiB7D,EAAMnE,EAAMC,EAAM,CAAC,EAAG,CACrC,KAAM,CAAE,MAAA+D,EAAO,OAAAtD,CAAO,EAAIyD,EACpB,CAAE,KAAM8D,CAAU,EAAIjE,EACtB,CAAE,IAAAb,EAAK,QAAAD,CAAQ,EAAIjD,EACzB,IAAIgC,EAAU,IAAI,IAClB,GAAIkB,IAAQtE,EACV,OAAQoJ,EAAW,CACjB,IAAK,IAAK,CACR,MAAMpG,EAAU7B,EAAK,mBACjB6B,GACW,KAAK,aAAanB,EAAQmB,EAAS,CAAE,QAAAqB,CAAQ,CAAC,GAEzDjB,EAAQ,IAAIJ,CAAO,EAGvB,KACF,CACA,IAAK,IAAK,CACR,KAAM,CAAE,WAAAG,CAAW,EAAIhC,EACvB,GAAIgC,EAAY,CACd,MAAMvB,EACJ,KAAKnB,GAAU,iBAAiB0C,EAAY,cAAY,EAC1D,IAAIH,EAAU,KAAK,UAAU7B,EAAMS,CAAM,EAIzC,IAHIoB,IAAY7B,IACd6B,EAAUpB,EAAO,YAAY,GAExBoB,GACQ,KAAK,aAAanB,EAAQmB,EAAS,CAAE,QAAAqB,CAAQ,CAAC,GAEzDjB,EAAQ,IAAIJ,CAAO,EAErBA,EAAUpB,EAAO,YAAY,CAEjC,CACA,KACF,CACA,IAAK,IAAK,CACR,MAAMA,EAAS,KAAKnB,GAAU,iBAAiBU,EAAM,cAAY,EACjE,IAAI6B,EAAUpB,EAAO,WAAW,EAChC,KAAOoB,GACQ,KAAK,aAAanB,EAAQmB,EAAS,CAAE,QAAAqB,CAAQ,CAAC,GAEzDjB,EAAQ,IAAIJ,CAAO,EAErBA,EAAUpB,EAAO,YAAY,EAE/B,KACF,CACA,IAAK,IACL,QAAS,CACP,KAAM,CAAE,MAAAa,EAAO,QAAA0G,CAAQ,EAAI,KAAK,qBAAqBtH,EAAQV,CAAI,EACjE,GAAIsB,EAAM,KACRW,EAAUX,UACD0G,EAAS,CAClB,MAAMvH,EAAS,KAAKnB,GAAU,iBAAiBU,EAAM,cAAY,EACjE,IAAI6B,EAAUpB,EAAO,SAAS,EAC9B,KAAOoB,GACQ,KAAK,aAAanB,EAAQmB,EAAS,CAAE,QAAAqB,CAAQ,CAAC,GAEzDjB,EAAQ,IAAIJ,CAAO,EAErBA,EAAUpB,EAAO,SAAS,CAE9B,CACF,CACF,KAEA,QAAQwH,EAAW,CACjB,IAAK,IAAK,CACR,MAAMpG,EAAU7B,EAAK,uBACjB6B,GACW,KAAK,aAAanB,EAAQmB,EAAS,CAAE,QAAAqB,CAAQ,CAAC,GAEzDjB,EAAQ,IAAIJ,CAAO,EAGvB,KACF,CACA,IAAK,IAAK,CACR,MAAMpB,EACJ,KAAKnB,GAAU,iBAAiBU,EAAK,WAAY,cAAY,EAC/D,IAAI6B,EAAUpB,EAAO,WAAW,EAChC,KAAOoB,GACDA,IAAY7B,GAGD,KAAK,aAAaU,EAAQmB,EAAS,CAAE,QAAAqB,CAAQ,CAAC,GAEzDjB,EAAQ,IAAIJ,CAAO,EAGvBA,EAAUpB,EAAO,YAAY,EAE/B,KACF,CACA,IAAK,IAAK,CACR,MAAMoB,EAAU7B,EAAK,WACjB6B,GACW,KAAK,aAAanB,EAAQmB,EAAS,CAAE,QAAAqB,CAAQ,CAAC,GAEzDjB,EAAQ,IAAIJ,CAAO,EAGvB,KACF,CACA,IAAK,IACL,QAAS,CACP,MAAMlB,EAAM,CAAC,EACb,IAAIkB,EAAU7B,EAAK,WACnB,KAAO6B,GACQ,KAAK,aAAanB,EAAQmB,EAAS,CAAE,QAAAqB,CAAQ,CAAC,GAEzDvC,EAAI,KAAKkB,CAAO,EAElBA,EAAUA,EAAQ,WAEhBlB,EAAI,SACNsB,EAAU,IAAI,IAAItB,EAAI,QAAQ,CAAC,EAEnC,CACF,CAEF,OAAOsB,CACT,CAUA,UAAUvB,EAAQT,EAAM,CAAC,EAAG,CAC1B,GAAI,CAAE,KAAAD,EAAM,OAAAS,CAAO,EAAIR,EAClBQ,IACHA,EAAS,KAAKb,IAEhB,IAAIsI,EACArG,EAAU,KAAK,UAAU7B,EAAMS,CAAM,EACzC,GAAIoB,EAQF,KAPIA,EAAQ,WAAa,gBAEdA,IAAY7B,GACjB6B,IAAY,KAAKnC,MACnBmC,EAAUpB,EAAO,SAAS,GAGvBoB,GAAS,CACd,IAAIQ,EAUJ,GATI,KAAK7C,GAAM,WAAa,eACtBqC,IAAY,KAAKrC,GACnB6C,EAAO,GAEPA,EAAO,KAAK7C,GAAM,SAASqC,CAAO,EAGpCQ,EAAO,GAELA,GACc,KAAK,aAAa3B,EAAQmB,CAAO,EACpC,CACXqG,EAAcrG,EACd,KACF,CAEFA,EAAUpB,EAAO,SAAS,CAC5B,CAEF,OAAOyH,GAAe,IACxB,CAQA,gBAAgB/D,EAAMgE,EAAY,CAChC,KAAM,CAAE,OAAAzH,CAAO,EAAIyD,EACb,CAACL,EAAM,GAAG+D,CAAY,EAAInH,EAC1B,CAAE,KAAMqD,CAAS,EAAID,EACrBgE,KAAW,oBAAiBhE,EAAK,IAAI,EACrCiE,EAAWF,EAAa,OAAS,EACvC,IAAIvG,EAAQ,IAAI,IACZ8G,EAAW,GACXJ,EAAU,GACd,OAAQjE,EAAU,CAChB,KAAK,cAAa,CAChB,GAAIoE,IAAejJ,EACJ,KAAK,aAAawB,EAAQ,KAAKlB,EAAK,IAE/C8B,EAAM,IAAI,KAAK9B,EAAK,EACpB4I,EAAW,YAEJD,IAAelJ,EAAe,CACvC,IAAI4C,EAAU,KAAKrC,GACnB,KAAOqC,GACQ,KAAK,aAAanB,EAAQmB,CAAO,IAE5CP,EAAM,IAAIO,CAAO,EACjBuG,EAAW,IAEbvG,EAAUA,EAAQ,UAEtB,SAAWsG,IAAepJ,GACf,KAAKW,GAAM,WAAa,eACjCsI,EAAU,OACL,CACL,MAAMhI,EAAO,KAAKN,GAAM,eAAeoI,CAAQ,EAC3C9H,IACFsB,EAAM,IAAItB,CAAI,EACdoI,EAAW,GAEf,CACA,KACF,CACA,KAAK,iBAAgB,CACnB,GAAID,IAAejJ,EACb,KAAKM,GAAM,WAAa,gBACxB,KAAKA,GAAM,UAAU,SAASsI,CAAQ,GACxCxG,EAAM,IAAI,KAAK9B,EAAK,UAEb2I,IAAelJ,EAAe,CACvC,IAAI4C,EAAU,KAAKrC,GACnB,KAAOqC,GACDA,EAAQ,WAAa,gBACnBA,EAAQ,UAAU,SAASiG,CAAQ,GACrCxG,EAAM,IAAIO,CAAO,EAEnBA,EAAUA,EAAQ,UAKxB,SAAWsG,IAAenJ,EAAc,CACtC,MAAMgB,EAAO,KAAK,UAAUU,EAAQ,CAClC,KAAM,KAAKlB,GACX,OAAQ,KAAKD,EACf,CAAC,EACD,GAAIS,EAAM,CACRsB,EAAM,IAAItB,CAAI,EACdoI,EAAW,GACX,KACF,CACF,SAAW,KAAK1I,GAAM,WAAa,0BACxB,KAAKA,GAAM,WAAa,eACjCsI,EAAU,OACL,CACL,MAAMrH,EACJ,CAAC,EAAE,MAAM,KAAK,KAAKjB,GAAM,uBAAuBoI,CAAQ,CAAC,EAC3D,GAAI,KAAKtI,GAAM,WAAa,eAC1B,UAAWQ,KAAQW,GACbX,IAAS,KAAKR,OAAS,eAAYQ,EAAM,KAAKR,EAAK,IACrD8B,EAAM,IAAItB,CAAI,OAGTW,EAAI,SACbW,EAAQ,IAAI,IAAIX,CAAG,EAEvB,CACA,KACF,CACA,KAAK,gBAAe,CAClB,GAAIwH,IAAejJ,EACb,KAAKM,GAAM,WAAa,gBACb,KAAK,aAAakB,EAAQ,KAAKlB,EAAK,IAE/C8B,EAAM,IAAI,KAAK9B,EAAK,EACpB4I,EAAW,YAGND,IAAelJ,EAAe,CACvC,IAAI4C,EAAU,KAAKrC,GACnB,KAAOqC,GACDA,EAAQ,WAAa,gBACV,KAAK,aAAanB,EAAQmB,CAAO,IAE5CP,EAAM,IAAIO,CAAO,EACjBuG,EAAW,IAEbvG,EAAUA,EAAQ,UAKxB,SAAWsG,IAAenJ,EAAc,CACtC,MAAMgB,EAAO,KAAK,UAAUU,EAAQ,CAClC,KAAM,KAAKlB,GACX,OAAQ,KAAKD,EACf,CAAC,EACD,GAAIS,EAAM,CACRsB,EAAM,IAAItB,CAAI,EACdoI,EAAW,GACX,KACF,CACF,SAAW,KAAK9I,GAAU,cAAgB,aAC/B,OAAO,KAAKwI,CAAQ,GACpB,KAAKpI,GAAM,WAAa,0BACxB,KAAKA,GAAM,WAAa,eACjCsI,EAAU,OACL,CACL,MAAMrH,EAAM,CAAC,EAAE,MAAM,KAAK,KAAKjB,GAAM,qBAAqBoI,CAAQ,CAAC,EACnE,GAAI,KAAKtI,GAAM,WAAa,eAC1B,UAAWQ,KAAQW,GACbX,IAAS,KAAKR,OAAS,eAAYQ,EAAM,KAAKR,EAAK,IACrD8B,EAAM,IAAItB,CAAI,OAGTW,EAAI,SACbW,EAAQ,IAAI,IAAIX,CAAG,EAEvB,CACA,KACF,CACA,KAAK,0BAAyB,CAE5B,KAAK,4BAA4BmH,CAAQ,EACzC,KACF,CACA,QACE,GAAIK,IAAelJ,GAAiB,kBAAgB,KAAK6I,CAAQ,GAC/D,GAAI,KAAKnI,IAAW,KAAKH,GAAM,WAAa,yBAAwB,CAClE,MAAMQ,EAAO,KAAK,4BAA4B8D,EAAM,KAAKtE,EAAK,EAC1DQ,GACFsB,EAAM,IAAItB,CAAI,CAElB,UACSmI,IAAejJ,EACX,KAAK,aAAawB,EAAQ,KAAKlB,EAAK,IAE/C8B,EAAM,IAAI,KAAK9B,EAAK,EACpB4I,EAAW,YAEJD,IAAelJ,EAAe,CACvC,IAAI4C,EAAU,KAAKrC,GACnB,KAAOqC,GACQ,KAAK,aAAanB,EAAQmB,CAAO,IAE5CP,EAAM,IAAIO,CAAO,EACjBuG,EAAW,IAEbvG,EAAUA,EAAQ,UAEtB,SAAWsG,IAAenJ,EAAc,CACtC,MAAMgB,EAAO,KAAK,UAAUU,EAAQ,CAClC,KAAM,KAAKlB,GACX,OAAQ,KAAKD,EACf,CAAC,EACD,GAAIS,EAAM,CACRsB,EAAM,IAAItB,CAAI,EACdoI,EAAW,GACX,KACF,CACF,MACEJ,EAAU,EAGhB,CACA,MAAO,CACL,SAAAD,EACA,SAAAK,EACA,MAAA9G,EACA,QAAA0G,CACF,CACF,CAQA,cAAcvG,EAAQ0G,EAAY,CAChC,MAAME,EAAY5G,EAAO,OACnB6G,EAAUD,EAAY,EACtBE,EAAY9G,EAAO,CAAC,EAC1B,IAAI0B,EACAgB,EACJ,GAAImE,EAAS,CACX,KAAM,CAAE,MAAOE,EAAY,OAAQ,CAAC,CAAE,KAAMC,CAAU,CAAC,CAAE,EAAIF,EACvDG,EAAWjH,EAAO4G,EAAY,CAAC,EAC/B,CAAE,OAAQ,CAAC,CAAE,KAAMM,CAAS,CAAC,CAAE,EAAID,EACzC,GAAIC,IAAa,2BAA2BA,IAAa,cACvDxF,EAAMrE,EACNqF,EAAOuE,UACED,IAAc,2BACdA,IAAc,cACvBtF,EAAMtE,EACNsF,EAAOoE,UACEJ,IAAepJ,EACxB,GAAIsJ,IAAc,EAAG,CACnB,KAAM,CAAE,KAAMJ,CAAU,EAAIO,EACxB,SAAS,KAAKP,CAAS,GACzB9E,EAAMrE,EACNqF,EAAOuE,IAEPvF,EAAMtE,EACNsF,EAAOoE,EAEX,MACEpF,EAAMtE,EACNsF,EAAOoE,MAEJ,CACL,IAAIlG,EACAuG,EACJ,SAAW,CAAE,MAAA5E,EAAO,OAAQ,CAACF,CAAI,CAAE,IAAKrC,EAAQ,CAC9C,KAAM,CAAE,KAAMsC,CAAS,EAAID,EACrBgE,KAAW,oBAAiBhE,EAAK,IAAI,EAC3C,GAAIC,IAAa,yBAAyB+D,IAAa,MAAO,CAC5DzF,EAAO,GACP,KACF,CACA,GAAI2B,GAAS,CAAC4E,EAAS,CACrB,KAAM,CAAE,KAAMX,CAAU,EAAIjE,EACxB,SAAS,KAAKiE,CAAS,IACzB5F,EAAO,GACPuG,EAAU,GAEd,CACF,CACIvG,GACFc,EAAMtE,EACNsF,EAAOoE,IAEPpF,EAAMrE,EACNqF,EAAOuE,EAEX,CACF,MACEvF,EAAMrE,EACNqF,EAAOoE,EAET,MAAO,CACL,QAAAD,EACA,IAAAnF,EACA,KAAAgB,CACF,CACF,CAOA,cAAcgE,EAAY,CACxB,MAAM9G,EAAM,KAAKlC,GAAK,OAAO,EAC7B,GAAIgJ,IAAepJ,GAAcoJ,IAAenJ,EAAc,CAC5D,MAAM6J,EAAe,IAAI,IACzB,IAAItH,EAAI,EACR,SAAW,CAAE,OAAAE,CAAO,IAAKJ,EAAK,CAC5B,KAAM,CAAE,IAAA8B,EAAK,KAAAgB,CAAK,EAAI,KAAK,cAAc1C,EAAQ0G,CAAU,EACrD,CACJ,SAAAJ,EAAU,SAAAK,EAAU,MAAA9G,EAAO,QAAA0G,CAC7B,EAAI,KAAK,gBAAgB7D,EAAMgE,CAAU,EACrC7G,EAAM,MACR,KAAKnC,GAAKoC,CAAC,EAAE,KAAO,GACpB,KAAK9B,GAAO8B,CAAC,EAAID,GACR0G,GACTa,EAAa,IAAI,IAAI,IAAI,CACvB,CAAC,QAAStH,CAAC,EACX,CAAC,OAAQ4C,CAAI,CACf,CAAC,CAAC,EAEJ,KAAKhF,GAAKoC,CAAC,EAAE,IAAM4B,EACnB,KAAKhE,GAAKoC,CAAC,EAAE,SAAW6G,GAAY,CAACL,EACrCxG,GACF,CACA,GAAIsH,EAAa,KAAM,CACrB,IAAI7I,EACAS,EACA,KAAKjB,KAAU,KAAKE,IAAS,KAAKF,GAAM,WAAa,gBACvDQ,EAAO,KAAKR,GACZiB,EAAS,KAAKlB,KAEdS,EAAO,KAAKN,GACZe,EAAS,KAAKb,IAEhB,IAAIwE,EAAW,KAAK,UAAUpE,EAAMS,CAAM,EAC1C,KAAO2D,GAAU,CACf,IAAI/B,EAAO,GAUX,GATI,KAAK7C,GAAM,WAAa,eACtB4E,IAAa,KAAK5E,GACpB6C,EAAO,GAEPA,EAAO,KAAK7C,GAAM,SAAS4E,CAAQ,EAGrC/B,EAAO,GAELA,EACF,UAAWyG,KAAeD,EAAc,CACtC,KAAM,CAAE,OAAAnI,CAAO,EAAIoI,EAAY,IAAI,MAAM,EAEzC,GADgB,KAAK,aAAapI,EAAQ0D,CAAQ,EACrC,CACX,MAAM2E,EAAQD,EAAY,IAAI,OAAO,EACrC,KAAK3J,GAAK4J,CAAK,EAAE,SAAW,GAC5B,KAAK5J,GAAK4J,CAAK,EAAE,KAAO,GACxB,KAAKtJ,GAAOsJ,CAAK,EAAE,IAAI3E,CAAQ,CACjC,CACF,CAEFA,EAAW3D,EAAO,SAAS,CAC7B,CACF,CACF,KAAO,CACL,IAAIc,EAAI,EACR,SAAW,CAAE,OAAAE,CAAO,IAAKJ,EAAK,CAC5B,MAAM8C,EAAO1C,EAAOA,EAAO,OAAS,CAAC,EAC/B,CACJ,SAAAsG,EAAU,SAAAK,EAAU,MAAA9G,CACtB,EAAI,KAAK,gBAAgB6C,EAAMgE,CAAU,EACrC7G,EAAM,OACR,KAAKnC,GAAKoC,CAAC,EAAE,KAAO,GACpB,KAAK9B,GAAO8B,CAAC,EAAID,GAEnB,KAAKnC,GAAKoC,CAAC,EAAE,IAAMzC,EACnB,KAAKK,GAAKoC,CAAC,EAAE,SAAW6G,GAAY,CAACL,EACrCxG,GACF,CACF,CACA,MAAO,CACL,KAAKpC,GACL,KAAKM,EACP,CACF,CAOA,WAAW6B,EAAO,CAChB,MAAMX,EAAM,CAAC,GAAGW,CAAK,EACrB,OAAIX,EAAI,OAAS,GACfA,EAAI,KAAK,CAACC,EAAGC,IAAM,CACjB,IAAIK,EACJ,SAAI,eAAYL,EAAGD,CAAC,EAClBM,EAAM,EAENA,EAAM,GAEDA,CACT,CAAC,EAEIP,CACT,CAOA,YAAYwH,EAAY,CACtB,KAAM,CAAC,GAAG/G,CAAQ,EAAI,KAAKjC,GACrBgD,EAAIf,EAAS,OACnB,IAAIE,EAAQ,IAAI,IAChB,QAASC,EAAI,EAAGA,EAAIY,EAAGZ,IAAK,CAC1B,KAAM,CAAE,OAAAE,EAAQ,IAAA0B,EAAK,SAAAiF,EAAU,KAAAY,CAAK,EAAI5H,EAASG,CAAC,EAC5C8G,EAAY5G,EAAO,OACzB,GAAI4G,GAAaW,EAAM,CACrB,MAAMC,EAAa,KAAKxJ,GAAO8B,CAAC,EAC1BgD,EAAY8D,EAAY,EAC9B,GAAI9D,IAAc,EAAG,CACnB,KAAM,CAAE,OAAQ,CAAC,CAAE,GAAGsD,CAAY,CAAE,EAAIpG,EAAO,CAAC,EAChD,IAAK0G,IAAepJ,GAAcoJ,IAAenJ,IAC7C,KAAKQ,GAAM,WAAa,gBAC1B,UAAWQ,KAAQiJ,EAEjB,IADab,GAAY,KAAK,aAAaP,EAAc7H,CAAI,IACjDA,IAAS,KAAKR,IAAS,KAAKA,GAAM,SAASQ,CAAI,IACzDsB,EAAM,IAAItB,CAAI,EACVmI,IAAepJ,GACjB,cAII8I,EAAa,QASvB,UAAW7H,KAAQiJ,EAEjB,IADab,GAAY,KAAK,aAAaP,EAAc7H,CAAI,KAE3DsB,EAAM,IAAItB,CAAI,EACVmI,IAAepJ,GACjB,cAbFoJ,IAAepJ,EAAY,CAC7B,MAAMmK,EAAI,CAAC,GAAG5H,CAAK,EACnBA,EAAQ,IAAI,IAAI,CAAC,GAAG4H,EAAG,GAAGD,CAAU,CAAC,CACvC,KAAO,CACL,KAAM,CAACjJ,CAAI,EAAI,CAAC,GAAGiJ,CAAU,EAC7B3H,EAAM,IAAItB,CAAI,CAChB,CAYJ,SAAWmD,IAAQtE,EAAU,CAC3B,GAAI,CAAE,MAAAmF,EAAO,OAAQmF,CAAY,EAAI1H,EAAO,CAAC,EAC7C,KAAM,CAAC,CAAE,GAAGoG,CAAY,EAAIsB,EAC5B,IAAIlH,EACJ,UAAWjC,KAAQiJ,EAAY,CAE7B,GADab,GAAY,KAAK,aAAaP,EAAc7H,CAAI,EACnD,CACR,IAAIwE,EAAY,IAAI,IAAI,CAACxE,CAAI,CAAC,EAC9B,QAASuC,EAAI,EAAGA,EAAI8F,EAAW9F,IAAK,CAClC,KAAM,CAAE,MAAO6G,EAAW,OAAA1I,CAAO,EAAIe,EAAOc,CAAC,EACvC5B,EAAM,CAAC,EACb,UAAWyD,KAAYI,EAAW,CAChC,MAAML,EAAO,CACX,MAAAH,EACA,OAAAtD,CACF,EACM8B,EAAI,KAAK,iBAAiB2B,EAAMC,EAAU,CAAE,IAAAjB,CAAI,CAAC,EACnDX,EAAE,MACJ7B,EAAI,KAAK,GAAG6B,CAAC,CAEjB,CACA,GAAI7B,EAAI,OACN,GAAI4B,IAAMgC,EAAW,CACnB,GAAI4D,IAAepJ,EAAY,CAC7B,MAAMmK,EAAI,CAAC,GAAG5H,CAAK,EACnBA,EAAQ,IAAI,IAAI,CAAC,GAAG4H,EAAG,GAAGvI,CAAG,CAAC,CAChC,KAAO,CACL,KAAM,CAACX,CAAI,EAAI,KAAK,WAAWW,CAAG,EAClCW,EAAM,IAAItB,CAAI,CAChB,CACAiC,EAAU,EACZ,MACE+B,EAAQoF,EACR5E,EAAY,IAAI,IAAI7D,CAAG,EACvBsB,EAAU,OAEP,CACLA,EAAU,GACV,KACF,CACF,CACF,MACEA,EAAU,GAEZ,GAAIA,GAAWkG,IAAepJ,EAC5B,KAEJ,CACA,GAAI,CAACkD,GAAWkG,IAAenJ,EAAc,CAC3C,KAAM,CAACqK,CAAS,EAAI,CAAC,GAAGJ,CAAU,EAClC,IAAIpH,EAAU,KAAK,UAAUsH,EAAa,CACxC,KAAME,EACN,OAAQ,KAAK9J,EACf,CAAC,EACD,KAAOsC,GAAS,CACd,IAAI2C,EAAY,IAAI,IAAI,CAAC3C,CAAO,CAAC,EACjC,QAASU,EAAI,EAAGA,EAAI8F,EAAW9F,IAAK,CAClC,KAAM,CAAE,MAAO6G,EAAW,OAAA1I,CAAO,EAAIe,EAAOc,CAAC,EACvC5B,EAAM,CAAC,EACb,UAAWyD,KAAYI,EAAW,CAChC,MAAML,EAAO,CACX,MAAAH,EACA,OAAAtD,CACF,EACM8B,EAAI,KAAK,iBAAiB2B,EAAMC,EAAU,CAAE,IAAAjB,CAAI,CAAC,EACnDX,EAAE,MACJ7B,EAAI,KAAK,GAAG6B,CAAC,CAEjB,CACA,GAAI7B,EAAI,OACN,GAAI4B,IAAMgC,EAAW,CACnB,KAAM,CAACvE,CAAI,EAAI,KAAK,WAAWW,CAAG,EAClCW,EAAM,IAAItB,CAAI,EACdiC,EAAU,EACZ,MACE+B,EAAQoF,EACR5E,EAAY,IAAI,IAAI7D,CAAG,EACvBsB,EAAU,OAEP,CACLA,EAAU,GACV,KACF,CACF,CACA,GAAIA,EACF,MAEFJ,EAAU,KAAK,UAAUsH,EAAa,CACpC,KAAMtH,EACN,OAAQ,KAAKtC,EACf,CAAC,EACDiF,EAAY,IAAI,IAAI,CAAC3C,CAAO,CAAC,CAC/B,CACF,CACF,KAAO,CACL,KAAM,CAAE,OAAQsH,CAAY,EAAI1H,EAAO8C,CAAS,EAC1C,CAAC,CAAE,GAAGsD,CAAY,EAAIsB,EAC5B,IAAIlH,EACJ,UAAWjC,KAAQiJ,EAAY,CAE7B,GADab,GAAY,KAAK,aAAaP,EAAc7H,CAAI,EACnD,CACR,IAAIwE,EAAY,IAAI,IAAI,CAACxE,CAAI,CAAC,EAC9B,QAASuC,EAAIgC,EAAY,EAAGhC,GAAK,EAAGA,IAAK,CACvC,MAAM4B,EAAO1C,EAAOc,CAAC,EACf5B,EAAM,CAAC,EACb,UAAWyD,KAAYI,EAAW,CAChC,MAAMhC,EAAI,KAAK,iBAAiB2B,EAAMC,EAAU,CAAE,IAAAjB,CAAI,CAAC,EACnDX,EAAE,MACJ7B,EAAI,KAAK,GAAG6B,CAAC,CAEjB,CACA,GAAI7B,EAAI,OACF4B,IAAM,GACRjB,EAAM,IAAItB,CAAI,EACdiC,EAAU,KAEVuC,EAAY,IAAI,IAAI7D,CAAG,EACvBsB,EAAU,QAEP,CACLA,EAAU,GACV,KACF,CACF,CACF,CACA,GAAIA,GAAWkG,IAAepJ,EAC5B,KAEJ,CACA,GAAI,CAACkD,GAAWkG,IAAenJ,EAAc,CAC3C,KAAM,CAACqK,CAAS,EAAI,CAAC,GAAGJ,CAAU,EAClC,IAAIpH,EAAU,KAAK,UAAUsH,EAAa,CACxC,KAAME,EACN,OAAQ,KAAK9J,EACf,CAAC,EACD,KAAOsC,GAAS,CACd,IAAI2C,EAAY,IAAI,IAAI,CAAC3C,CAAO,CAAC,EACjC,QAASU,EAAIgC,EAAY,EAAGhC,GAAK,EAAGA,IAAK,CACvC,MAAM4B,EAAO1C,EAAOc,CAAC,EACf5B,EAAM,CAAC,EACb,UAAWyD,KAAYI,EAAW,CAChC,MAAMhC,EAAI,KAAK,iBAAiB2B,EAAMC,EAAU,CAAE,IAAAjB,CAAI,CAAC,EACnDX,EAAE,MACJ7B,EAAI,KAAK,GAAG6B,CAAC,CAEjB,CACA,GAAI7B,EAAI,OACF4B,IAAM,GACRjB,EAAM,IAAIO,CAAO,EACjBI,EAAU,KAEVuC,EAAY,IAAI,IAAI7D,CAAG,EACvBsB,EAAU,QAEP,CACLA,EAAU,GACV,KACF,CACF,CACA,GAAIA,EACF,MAEFJ,EAAU,KAAK,UAAUsH,EAAa,CACpC,KAAMtH,EACN,OAAQ,KAAKtC,EACf,CAAC,EACDiF,EAAY,IAAI,IAAI,CAAC3C,CAAO,CAAC,CAC/B,CACF,CACF,CACF,CACF,CACA,OAAOP,CACT,CAOA,MAAM6G,EAAY,CAChB,OAAIA,IAAepJ,GAAcoJ,IAAenJ,KAC9C,KAAKO,GAAU,KAAKD,GAAU,iBAAiB,KAAKE,GAAO,cAAY,GAEzE,KAAK,cAAc2I,CAAU,EACf,KAAK,YAAYA,CAAU,CAE3C,CAMA,SAAU,CACR,GAAI,KAAK3I,GAAM,WAAa,eAAc,CACxC,MAAMe,EAAM,mBAAmB,KAAKf,GAAM,QAAQ,GAClD,KAAK,SAAS,IAAI,UAAUe,CAAG,CAAC,CAClC,CACA,IAAIW,EACJ,GAAI,CACF,MAAMI,EAAQ,KAAK,MAAMpC,CAAW,EAChCoC,EAAM,OACRJ,EAAMI,EAAM,IAAI,KAAK9B,EAAK,EAE9B,OAAS,EAAG,CACV,KAAK,SAAS,CAAC,CACjB,CACA,MAAO,CAAC,CAAC0B,CACX,CAMA,SAAU,CACR,GAAI,KAAK1B,GAAM,WAAa,eAAc,CACxC,MAAMe,EAAM,mBAAmB,KAAKf,GAAM,QAAQ,GAClD,KAAK,SAAS,IAAI,UAAUe,CAAG,CAAC,CAClC,CACA,IAAIW,EACJ,GAAI,CACF,MAAMI,EAAQ,KAAK,MAAMrC,CAAa,EACtC,IAAIe,EAAO,KAAKR,GAChB,KAAOQ,GAAM,CACX,GAAIsB,EAAM,IAAItB,CAAI,EAAG,CACnBkB,EAAMlB,EACN,KACF,CACAA,EAAOA,EAAK,UACd,CACF,OAAS,EAAG,CACV,KAAK,SAAS,CAAC,CACjB,CACA,OAAOkB,GAAO,IAChB,CAMA,eAAgB,CACd,IAAIA,EACJ,GAAI,CACF,MAAMI,EAAQ,KAAK,MAAMtC,CAAY,EACrCsC,EAAM,OAAO,KAAK9B,EAAK,EACnB8B,EAAM,OACR,CAACJ,CAAG,EAAI,KAAK,WAAWI,CAAK,EAEjC,OAAS,EAAG,CACV,KAAK,SAAS,CAAC,CACjB,CACA,OAAOJ,GAAO,IAChB,CAOA,kBAAmB,CACjB,IAAIA,EACJ,GAAI,CACF,MAAMI,EAAQ,KAAK,MAAMvC,CAAU,EACnCuC,EAAM,OAAO,KAAK9B,EAAK,EACnB8B,EAAM,OACRJ,EAAM,KAAK,WAAWI,CAAK,EAE/B,OAAS,EAAG,CACV,KAAK,SAAS,CAAC,CACjB,CACA,OAAOJ,GAAO,CAAC,CACjB,CACF", "names": ["matcher_exports", "__export", "Matcher", "__toCommonJS", "import_is_potential_custom_element_name", "import_dom_util", "import_parser", "import_constant", "DIR_NEXT", "DIR_PREV", "TARGET_ALL", "TARGET_FIRST", "TARGET_LINEAL", "TARGET_SELF", "#ast", "#bit", "#cache", "#document", "#finder", "#node", "#nodes", "#root", "#shadow", "#tree", "#warn", "#window", "selector", "node", "opt", "warn", "e", "document", "root", "parent", "msg", "filter", "walker", "leaves", "arr", "a", "b", "typeA", "typeB", "bitA", "bitB", "res", "cssAst", "branches", "ast", "nodes", "i", "items", "branch", "item", "nextItem", "current", "refNode", "anb", "reverse", "parentNode", "matched", "selectorBranches", "l", "selectorNodes", "bool", "nth", "j", "m", "localName", "prefix", "itemLocalName", "itemPrefix", "nthName", "nthIdentName", "identName", "anbMap", "astName", "forgive", "dir", "lang<PERSON><PERSON>", "regExtendedLang", "lang<PERSON><PERSON>", "langSub", "langRest", "extendedMain", "extendedSub", "len", "extendedRest", "value", "leaf", "leafType", "combo", "twig<PERSON>eaves", "itemType", "twig", "nextNode", "astData", "twigBranches", "lastIndex", "nextNodes", "<PERSON>t<PERSON><PERSON><PERSON><PERSON>", "selectors", "css", "leavesSet", "regAnchor", "regFormCtrl", "regFormValidity", "regInteract", "regT<PERSON><PERSON><PERSON><PERSON>", "regTypeDate", "regTypeRange", "regTypeText", "href", "origin", "pathname", "attrURL", "hash", "id", "isCustomElementName", "targetNode", "nodeName", "checked", "regTypeReset", "regTypeSubmit", "form", "isMultiple", "defaultOpt", "inputType", "node1", "node2", "astFlags", "astMatcher", "astValue", "attributes", "caseInsensitive", "astAttrName", "attrV<PERSON>ues", "astAttrPrefix", "astAttrLocalName", "itemName", "itemValue", "itemNamePrefix", "itemNameLocalName", "astAttrIdentValue", "astAttrStringValue", "attrValue", "astPrefix", "astNodeName", "nodePrefix", "host", "astType", "baseNode", "filterLeaves", "leafName", "compound", "pending", "comboName", "matchedNode", "targetType", "filtered", "branchLen", "complex", "firstTwig", "firstCombo", "firstType", "lastTwig", "lastType", "sibling", "pendingItems", "pendingItem", "index", "find", "entryNodes", "n", "entryLeaves", "nextCombo", "entryNode"]}