/**
 * Tests for API Service
 * =====================
 *
 * This module contains tests for the API service.
 * All tests use mocked axios to avoid dependencies on backend.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'

// Mock axios before importing the service
const mockAxiosInstance = {
  get: vi.fn(),
  post: vi.fn(),
  put: vi.fn(),
  delete: vi.fn(),
  interceptors: {
    request: { use: vi.fn() },
    response: { use: vi.fn() }
  }
}

vi.mock('axios', () => ({
  default: {
    create: vi.fn(() => mockAxiosInstance)
  }
}))

// Import after mocking
import apiService from '../src/services/api'

describe('API Service', () => {
  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks()
  })

  it('getStatus calls correct endpoint', async () => {
    // Arrange
    const mockResponse = { data: { connected: true } }
    mockAxiosInstance.get.mockResolvedValue(mockResponse)

    // Act
    const result = await apiService.getStatus()

    // Assert
    expect(mockAxiosInstance.get).toHaveBeenCalledWith('/status')
    expect(result).toBe(mockResponse)
  })

  it('getHealth calls correct endpoint', async () => {
    // Arrange
    const mockResponse = { data: { healthy: true } }
    mockAxiosInstance.get.mockResolvedValue(mockResponse)
    
    // Act
    const result = await apiService.getHealth()
    
    // Assert
    expect(mockAxiosInstance.get).toHaveBeenCalledWith('/status/health')
    expect(result).toBe(mockResponse)
  })

  it('getConfig calls correct endpoint', async () => {
    // Arrange
    const mockResponse = { data: { build_space_diameter: 100 } }
    mockAxiosInstance.get.mockResolvedValue(mockResponse)
    
    // Act
    const result = await apiService.getConfig()
    
    // Assert
    expect(mockAxiosInstance.get).toHaveBeenCalledWith('/config')
    expect(result).toBe(mockResponse)
  })

  it('setConfig calls correct endpoint with data', async () => {
    // Arrange
    const configData = { build_space_diameter: 120 }
    const mockResponse = { data: { status: 'success' } }
    mockAxiosInstance.put.mockResolvedValue(mockResponse)
    
    // Act
    const result = await apiService.setConfig(configData)
    
    // Assert
    expect(mockAxiosInstance.put).toHaveBeenCalledWith('/config', configData)
    expect(result).toBe(mockResponse)
  })

  it('getDrums calls correct endpoint', async () => {
    // Arrange
    const mockResponse = { data: { drums: [] } }
    mockAxiosInstance.get.mockResolvedValue(mockResponse)
    
    // Act
    const result = await apiService.getDrums()
    
    // Assert
    expect(mockAxiosInstance.get).toHaveBeenCalledWith('/drums')
    expect(result).toBe(mockResponse)
  })

  it('getDrum calls correct endpoint with drum ID', async () => {
    // Arrange
    const drumId = 1
    const mockResponse = { data: { id: 1, name: 'Drum 1' } }
    mockAxiosInstance.get.mockResolvedValue(mockResponse)
    
    // Act
    const result = await apiService.getDrum(drumId)
    
    // Assert
    expect(mockAxiosInstance.get).toHaveBeenCalledWith('/drums/1')
    expect(result).toBe(mockResponse)
  })

  it('handles API errors correctly', async () => {
    // Arrange
    const error = new Error('Network error')
    mockAxiosInstance.get.mockRejectedValue(error)
    
    // Act & Assert
    await expect(apiService.getStatus()).rejects.toThrow('Network error')
    expect(mockAxiosInstance.get).toHaveBeenCalledWith('/status')
  })

  it('handles different drum IDs correctly', async () => {
    // Arrange
    const mockResponse = { data: { id: 5, name: 'Drum 5' } }
    mockAxiosInstance.get.mockResolvedValue(mockResponse)
    
    // Act
    await apiService.getDrum(5)
    
    // Assert
    expect(mockAxiosInstance.get).toHaveBeenCalledWith('/drums/5')
  })
})
