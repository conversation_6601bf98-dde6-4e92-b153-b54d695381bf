# Recoater HMI - User Guide

This guide explains how to use the Custom HMI to operate the Aerosint SPD Recoater.

## 1. Starting the System

### Prerequisites
- Aerosint Recoater hardware powered on and connected to network (IP: *************)
- Python 3.8+ installed for backend
- Node.js 16+ installed for frontend

### Starting the Backend
1. Navigate to the `backend` directory
2. Install dependencies: `pip install -r requirements.txt`
3. Start the FastAPI server: `python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000`
4. Backend will be available at `http://localhost:8000`

### Starting the Frontend
1. Navigate to the `frontend` directory
2. Install dependencies: `npm install`
3. Start the development server: `npm run dev`
4. Frontend will be available at `http://localhost:5173`

### Accessing the HMI
1. Open a web browser (Chrome, Firefox, or Edge recommended)
2. Navigate to `http://localhost:5173`
3. The application will automatically connect to the backend and attempt to communicate with the recoater



## 2. The Main Interface

The interface is divided into several main tabs, accessible from a navigation menu on the left:

-   **Status:** The main landing page showing the overall system connection status and detailed information.

-   **Axis:** Manual controls for the X and Z axes (Phase 2).

-   **Recoater:** Controls for the individual drums, hoppers, and the leveler (Phase 2).

-   **Print:** For loading print files and managing the printing process (Phase 2).

-   **Configuration:** For setting system-level parameters like the build area (Phase 2).

### 2.1. Status Indicator

In the top-right corner of the header, you'll see a real-time status indicator:

- **Green dot + "Connected"**: HMI is successfully communicating with both the backend and recoater hardware
- **Red dot + "Disconnected"**: Connection to the backend has been lost
- **Orange dot + "Error"**: Connected to backend but the recoater hardware has issues

The status indicator updates automatically in real-time via WebSocket connection.

### 2.2. Status Page

The main Status page provides detailed information about the system:

**Connection Status Card:**
- Backend connection status
- Recoater hardware connection status
- Last update timestamp

**System Information Card:**
- Current recoater state (ready, printing, error, etc.)
- Additional system details when available

**Error Information Card:**
- Displays when there are connection or hardware errors
- Shows detailed error messages for troubleshooting

**Manual Refresh:**
- Use the "Refresh Status" button to manually update the status
- Automatic updates continue via WebSocket in the background



### 2.2. Using the "Axis" Window

This window allows you to manually move the machine's axes.

1.  **Homing:** Before any operation, it is recommended to **Home** each axis by pressing the "Home" button (`🏠`).

2.  **Manual Movement:**

    -   Enter a `Distance (mm)` and `Speed (mm/s)`.

    -   Use the arrow buttons (`←`, `→`, `↑`, `↓`) to move the axes by the specified distance.

3.  **Gripper:** Use the toggle switch to activate or deactivate the punch gripper.

### 2.3. Using the "Recoater" Window

This window is for preparing the recoater drums.

1.  **Select a Component:** Click on the graphical representation of a drum, hopper, or leveler to show its specific controls on the right.

2.  **Drum Control:**

    -   Set the desired `Suction` and `Ejection` pressures.

    -   To manually rotate a drum, set a `Speed` and a number of `Turns`, then press the "Rotate" button (`🔄`).

3.  **Hopper Control (Scraping Blade):**

    -   Select which screw to move (`Front`, `Back`, or `Both`).

    -   Enter a `Distance (µm)` and use the up/down arrows to move the blade.

    -   **Always Home the blade (`🏠`) on startup.**



### 2.4. Running a Print Job ("Print" Window)

This is the main window for executing a print.

1.  **Load Geometry:** For each drum you intend to use, click the "Upload" button (`⬆️`) and select the appropriate `.CLI` or `.PNG` file for that material.

2.  **Set Parameters:** On the right-hand panel, configure all print parameters, such as `Patterning speed`, `Filling drum`, and `Layer thickness`.

3.  **Preview:** The central area will show a preview of the deposited powder based on your loaded files.

4.  **Start Printing:** Once everything is configured, press the **"Start Printing"** button.

5.  **Stop Printing:** To cancel the active job, press the **"Stop Printing"** button.

