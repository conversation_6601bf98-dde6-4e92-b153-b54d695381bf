{"version": 3, "sources": ["../../src/index.js"], "sourcesContent": ["/*!\n * DOM Selector - A CSS selector engine.\n * @license MIT\n * @copyright asamuzaK (Kazz)\n * @see {@link https://github.com/asamuzaK/domSelector/blob/main/LICENSE}\n */\n\n/* import */\nimport { Matcher } from './js/matcher.js';\n\n/**\n * matches\n * @param {string} selector - CSS selector\n * @param {object} node - Element node\n * @param {object} [opt] - options\n * @param {boolean} [opt.warn] - console warn e.g. unsupported pseudo-class\n * @returns {boolean} - `true` if matched, `false` otherwise\n */\nexport const matches = (selector, node, opt) =>\n  new Matcher(selector, node, opt).matches();\n\n/**\n * closest\n * @param {string} selector - CSS selector\n * @param {object} node - Element node\n * @param {object} [opt] - options\n * @param {boolean} [opt.warn] - console warn e.g. unsupported pseudo-class\n * @returns {?object} - matched node\n */\nexport const closest = (selector, node, opt) =>\n  new Matcher(selector, node, opt).closest();\n\n/**\n * querySelector\n * @param {string} selector - CSS selector\n * @param {object} node - Document, DocumentFragment or Element node\n * @param {object} [opt] - options\n * @param {boolean} [opt.warn] - console warn e.g. unsupported pseudo-class\n * @returns {?object} - matched node\n */\nexport const querySelector = (selector, node, opt) =>\n  new Matcher(selector, node, opt).querySelector();\n\n/**\n * querySelectorAll\n * NOTE: returns Array, not NodeList\n * @param {string} selector - CSS selector\n * @param {object} node - Document, DocumentFragment or Element node\n * @param {object} [opt] - options\n * @param {boolean} [opt.warn] - console warn e.g. unsupported pseudo-class\n * @returns {Array.<object|undefined>} - array of matched nodes\n */\nexport const querySelectorAll = (selector, node, opt) =>\n  new Matcher(selector, node, opt).querySelectorAll();\n"], "mappings": "4ZAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,aAAAE,EAAA,YAAAC,EAAA,kBAAAC,EAAA,qBAAAC,IAAA,eAAAC,EAAAN,GAQA,IAAAO,EAAwB,2BARxB;AAAA;AAAA;AAAA;AAAA;AAAA,GAkBO,MAAMJ,EAAU,CAACK,EAAUC,EAAMC,IACtC,IAAI,UAAQF,EAAUC,EAAMC,CAAG,EAAE,QAAQ,EAU9BR,EAAU,CAACM,EAAUC,EAAMC,IACtC,IAAI,UAAQF,EAAUC,EAAMC,CAAG,EAAE,QAAQ,EAU9BN,EAAgB,CAACI,EAAUC,EAAMC,IAC5C,IAAI,UAAQF,EAAUC,EAAMC,CAAG,EAAE,cAAc,EAWpCL,EAAmB,CAACG,EAAUC,EAAMC,IAC/C,IAAI,UAAQF,EAAUC,EAAMC,CAAG,EAAE,iBAAiB", "names": ["src_exports", "__export", "closest", "matches", "querySelector", "querySelectorAll", "__toCommonJS", "import_matcher", "selector", "node", "opt"]}